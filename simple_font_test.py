#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的字体测试
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np

def test_simple_font():
    """简单的字体测试"""
    print("开始简单字体测试...")
    
    # 创建测试数据
    x = np.arange(10)
    y = np.random.rand(10)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(8, 6))
    ax.plot(x, y, label="测试数据")
    
    # 测试不同的字体设置方法
    try:
        # 方法1: 使用FontProperties
        font_prop = fm.FontProperties(family=['SimSun', 'SimHei'])
        ax.set_xlabel("时间步", fontproperties=font_prop)
        ax.set_ylabel("数值", fontproperties=font_prop)
        ax.legend(prop=font_prop)
        print("✓ FontProperties 方法成功")
        
        plt.savefig('simple_font_test.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        return True
        
    except Exception as e:
        print(f"✗ FontProperties 方法失败: {e}")
        
        # 方法2: 使用rcParams
        try:
            plt.rcParams['font.sans-serif'] = ['SimSun', 'SimHei']
            plt.rcParams['axes.unicode_minus'] = False
            
            ax.set_xlabel("时间步")
            ax.set_ylabel("数值")
            ax.legend()
            print("✓ rcParams 方法成功")
            
            plt.savefig('simple_font_test_rcparams.png', dpi=150, bbox_inches='tight')
            plt.close()
            
            return True
            
        except Exception as e2:
            print(f"✗ rcParams 方法也失败: {e2}")
            return False

if __name__ == "__main__":
    test_simple_font()
