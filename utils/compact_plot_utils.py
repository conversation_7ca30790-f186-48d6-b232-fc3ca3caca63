#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧凑型绘图工具模块
专门用于生成小尺寸、高分辨率的图片
- 图片宽度控制在6cm以内
- 标签字体使用6号宋体
- 分辨率300 DPI
"""

import matplotlib.pyplot as plt
import numpy as np
import os
import matplotlib.font_manager as fm

def configure_compact_font():
    """
    配置紧凑型绘图的中文字体
    """
    # 设置中文字体为SimSun（宋体）
    plt.rcParams['font.sans-serif'] = ['SimSun', 'SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    plt.rcParams['font.size'] = 6  # 设置基础字体大小为6号

def get_compact_font_prop(size=None, weight='normal'):
    """
    获取紧凑型绘图的中文字体属性
    
    参数:
    - size: 字体大小，如果为None则使用默认大小
    - weight: 字体粗细 ('normal', 'bold')
    """
    try:
        # 使用SimSun作为主要字体
        font_prop = fm.FontProperties(
            family=['SimSun', 'SimHei', 'Microsoft YaHei'],
            size=size,
            weight=weight
        )
        return font_prop
    except:
        # 如果失败，使用默认字体
        return fm.FontProperties(size=size, weight=weight)

def safe_savefig_compact(save_path, dpi=300, **kwargs):
    """
    安全的图片保存函数，专用于紧凑型绘图
    """
    # 使用基本和兼容的参数
    plt.savefig(save_path, dpi=dpi, bbox_inches='tight', 
               facecolor='white', edgecolor='none')

def create_compact_plot(predicted_values, actual_values, station_id=None, 
                       title=None, save_path=None, show_plot=False, 
                       width_cm=6, height_cm=4, dpi=300):
    """
    创建紧凑型预测结果绘图
    
    参数:
    - predicted_values: 预测值列表
    - actual_values: 实际值列表  
    - station_id: 站点ID
    - title: 图表标题（通常隐藏）
    - save_path: 保存路径
    - show_plot: 是否显示图表
    - width_cm: 图表宽度（厘米），默认6cm
    - height_cm: 图表高度（厘米），默认4cm
    - dpi: 图片分辨率，默认300
    """
    
    # 配置紧凑型字体
    configure_compact_font()
    
    # 将厘米转换为英寸 (1英寸 = 2.54厘米)
    width_inch = width_cm / 2.54
    height_inch = height_cm / 2.54
    
    # 创建图表，使用紧凑尺寸
    fig, ax = plt.subplots(figsize=(width_inch, height_inch), dpi=100)
    
    # 创建时间轴，从0开始显示起始值
    time_steps = np.arange(len(predicted_values))
    
    # 绘制预测值和实际值，使用较细的线条适应小图
    ax.plot(time_steps, predicted_values, color="red", linewidth=1.0, 
            label="预测值", alpha=0.8)
    ax.plot(time_steps, actual_values, color="blue", linewidth=1.0, 
            label="实际值", alpha=0.8)
    
    # 设置坐标轴刻度线在内侧，使用较小的刻度
    ax.tick_params(axis='both', which='major', direction='in', length=3, width=0.5, labelsize=5)
    ax.tick_params(axis='both', which='minor', direction='in', length=2, width=0.3)
    
    # 确保显示起始值 - 设置x轴从0开始
    ax.set_xlim(0, len(predicted_values) - 1)
    
    # 设置y轴范围，确保显示所有数据点
    all_values = list(predicted_values) + list(actual_values)
    y_min, y_max = min(all_values), max(all_values)
    y_range = y_max - y_min
    ax.set_ylim(y_min - 0.05 * y_range, y_max + 0.05 * y_range)
    
    # 设置网格，使用较细的线条
    ax.grid(True, linestyle='--', alpha=0.3, linewidth=0.3)
    
    # 获取紧凑型中文字体
    label_font = get_compact_font_prop(size=6, weight='bold')
    legend_font = get_compact_font_prop(size=5, weight='normal')
    tick_font = get_compact_font_prop(size=4)
    
    # 设置标签 - 使用6号字体
    ax.set_xlabel("时间步", fontproperties=label_font, labelpad=2)
    ax.set_ylabel("客流量", fontproperties=label_font, labelpad=2)
    
    # 隐藏标题
    # if title:
    #     title_font = get_compact_font_prop(size=7, weight='bold')
    #     ax.set_title(title, fontproperties=title_font, pad=5)
    
    # 设置图例 - 使用小字体和紧凑布局
    legend = ax.legend(loc='upper right', frameon=True, fancybox=False, 
                      shadow=False, framealpha=0.9, prop=legend_font,
                      edgecolor='gray', facecolor='white')
    legend.get_frame().set_linewidth(0.5)
    
    # 设置刻度标签字体
    for label in ax.get_xticklabels() + ax.get_yticklabels():
        label.set_fontproperties(tick_font)
    
    # 调整布局，使用紧凑间距
    plt.tight_layout(pad=0.5)
    
    # 保存图表
    if save_path:
        # 确保保存目录存在
        save_dir = os.path.dirname(save_path)
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir)
        safe_savefig_compact(save_path, dpi=dpi)
    
    # 显示图表
    if show_plot:
        plt.show()
    else:
        plt.close()

def create_compact_peak_analysis_plot(predicted_values, actual_values, peaks_pred_time, peaks_pred_val,
                                     peaks_true_time, peaks_true_val, valleys_pred_time=None, 
                                     valleys_pred_val=None, valleys_true_time=None, valleys_true_val=None,
                                     station_id=None, title=None, save_path=None, show_plot=False,
                                     width_cm=6, height_cm=4, dpi=300):
    """
    创建紧凑型峰值分析绘图
    
    参数:
    - predicted_values: 预测值列表
    - actual_values: 实际值列表
    - peaks_pred_time, peaks_pred_val: 预测峰值的时间和值
    - peaks_true_time, peaks_true_val: 真实峰值的时间和值
    - valleys_*: 低谷相关参数（可选）
    - 其他参数同create_compact_plot
    """
    
    # 配置紧凑型字体
    configure_compact_font()
    
    # 将厘米转换为英寸
    width_inch = width_cm / 2.54
    height_inch = height_cm / 2.54
    
    # 创建图表，使用紧凑尺寸
    fig, ax = plt.subplots(figsize=(width_inch, height_inch), dpi=100)
    
    # 创建时间轴
    time_steps = np.arange(len(predicted_values))
    
    # 绘制主要曲线，使用较细的线条
    ax.plot(time_steps, predicted_values, color="red", linewidth=1.0, 
            label="预测值", alpha=0.8)
    ax.plot(time_steps, actual_values, color="blue", linewidth=1.0, 
            label="实际值", alpha=0.8)
    
    # 绘制峰值点，使用较小的标记
    ax.scatter(peaks_pred_time, peaks_pred_val, color="darkred", s=15, 
              marker="^", label="峰值预测", zorder=5, edgecolors='black', linewidth=0.5)
    ax.scatter(peaks_true_time, peaks_true_val, color="darkblue", s=15, 
              marker="^", label="峰值真实", zorder=5, edgecolors='black', linewidth=0.5)
    
    # 绘制低谷点（如果提供），使用较小的标记
    if valleys_pred_time is not None and valleys_pred_val is not None:
        ax.scatter(valleys_pred_time, valleys_pred_val, color="orange", s=15, 
                  marker="v", label="低谷预测", zorder=5, edgecolors='black', linewidth=0.5)
    if valleys_true_time is not None and valleys_true_val is not None:
        ax.scatter(valleys_true_time, valleys_true_val, color="green", s=15, 
                  marker="v", label="低谷真实", zorder=5, edgecolors='black', linewidth=0.5)
    
    # 设置坐标轴刻度线在内侧，使用较小的刻度
    ax.tick_params(axis='both', which='major', direction='in', length=3, width=0.5, labelsize=5)
    ax.tick_params(axis='both', which='minor', direction='in', length=2, width=0.3)
    
    # 设置坐标轴范围
    ax.set_xlim(0, len(predicted_values) - 1)
    
    all_values = list(predicted_values) + list(actual_values)
    y_min, y_max = min(all_values), max(all_values)
    y_range = y_max - y_min
    ax.set_ylim(y_min - 0.05 * y_range, y_max + 0.05 * y_range)
    
    # 设置网格，使用较细的线条
    ax.grid(True, linestyle='--', alpha=0.3, linewidth=0.3)
    
    # 获取紧凑型中文字体
    label_font = get_compact_font_prop(size=6, weight='bold')
    legend_font = get_compact_font_prop(size=4, weight='normal')
    tick_font = get_compact_font_prop(size=4)
    
    # 设置标签 - 使用6号字体
    ax.set_xlabel("时间步", fontproperties=label_font, labelpad=2)
    ax.set_ylabel("客流量", fontproperties=label_font, labelpad=2)
    
    # 隐藏标题
    # if title:
    #     title_font = get_compact_font_prop(size=7, weight='bold')
    #     ax.set_title(title, fontproperties=title_font, pad=5)
    
    # 设置图例 - 放在图表外侧，使用小字体
    legend = ax.legend(bbox_to_anchor=(1.02, 1), loc='upper left', frameon=True, 
                      fancybox=False, shadow=False, framealpha=0.9, prop=legend_font,
                      edgecolor='gray', facecolor='white')
    legend.get_frame().set_linewidth(0.5)
    
    # 设置刻度标签字体
    for label in ax.get_xticklabels() + ax.get_yticklabels():
        label.set_fontproperties(tick_font)
    
    # 调整布局
    plt.tight_layout(pad=0.5)
    
    # 保存图表
    if save_path:
        save_dir = os.path.dirname(save_path)
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir)
        safe_savefig_compact(save_path, dpi=dpi)
    
    # 显示图表
    if show_plot:
        plt.show()
    else:
        plt.close()

def create_compact_multiple_stations_plot(x_data, y_data, station_ids, save_dir, 
                                         model_name="", width_cm=6, height_cm=4, dpi=300):
    """
    为多个站点创建紧凑型的预测结果图表
    
    参数:
    - x_data: 预测值数据列表，每个元素对应一个站点
    - y_data: 实际值数据列表，每个元素对应一个站点
    - station_ids: 站点ID列表
    - save_dir: 保存目录
    - model_name: 模型名称（用于文件命名）
    - width_cm: 图表宽度（厘米）
    - height_cm: 图表高度（厘米）
    - dpi: 图片分辨率
    """
    
    # 确保保存目录存在
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 为每个站点创建紧凑型图表
    for i, station_id in enumerate(station_ids):
        if i < len(x_data) and i < len(y_data):
            save_path = os.path.join(save_dir, f'compact_station_{station_id}_prediction.png')
            if model_name:
                save_path = os.path.join(save_dir, f'compact_{model_name}_station_{station_id}_prediction.png')
            
            create_compact_plot(
                predicted_values=x_data[i],
                actual_values=y_data[i],
                station_id=station_id,
                save_path=save_path,
                show_plot=False,
                width_cm=width_cm,
                height_cm=height_cm,
                dpi=dpi
            )
    
    print(f"已为{len(station_ids)}个站点生成紧凑型预测结果图表，保存在: {save_dir}")
