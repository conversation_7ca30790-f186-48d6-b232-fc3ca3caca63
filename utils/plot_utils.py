import matplotlib.pyplot as plt
import numpy as np
import os
import matplotlib.font_manager as fm

def configure_chinese_font():
    """
    配置中文字体，优化清晰度
    """
    # 设置中文字体，优先使用清晰度更好的字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun']
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    plt.rcParams['font.size'] = 10  # 增大基础字体大小提高清晰度

    # 设置字体渲染参数以提高清晰度
    plt.rcParams['font.weight'] = 'normal'  # 字体粗细
    plt.rcParams['axes.linewidth'] = 1.2    # 坐标轴线宽
    plt.rcParams['xtick.major.size'] = 6    # x轴主刻度大小
    plt.rcParams['ytick.major.size'] = 6    # y轴主刻度大小
    plt.rcParams['xtick.minor.size'] = 3    # x轴次刻度大小
    plt.rcParams['ytick.minor.size'] = 3    # y轴次刻度大小

def get_chinese_font_prop(size=None, weight='normal'):
    """
    获取中文字体属性，避免字体名称解析错误

    参数:
    - size: 字体大小，如果为None则使用默认大小
    - weight: 字体粗细 ('normal', 'bold')
    """
    try:
        # 使用清晰度更好的字体顺序：微软雅黑 > 黑体 > 宋体
        font_prop = fm.FontProperties(
            family=['Microsoft YaHei', 'SimHei', 'SimSun'],
            size=size,
            weight=weight
        )
        return font_prop
    except:
        # 如果失败，使用默认字体
        return fm.FontProperties(size=size, weight=weight)

def create_improved_plot(predicted_values, actual_values, station_id=None, 
                        title=None, save_path=None, show_plot=False, 
                        figsize=(12, 6), dpi=300):
    """
    创建改进的预测结果绘图
    
    参数:
    - predicted_values: 预测值列表
    - actual_values: 实际值列表  
    - station_id: 站点ID
    - title: 图表标题
    - save_path: 保存路径
    - show_plot: 是否显示图表
    - figsize: 图表尺寸
    - dpi: 图片分辨率
    """
    
    # 配置中文字体
    configure_chinese_font()
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 创建时间轴，从0开始显示起始值
    time_steps = np.arange(len(predicted_values))
    
    # 绘制预测值和实际值
    line1 = ax.plot(time_steps, predicted_values, color="red", linewidth=2, 
                    label="预测值", alpha=0.8)
    line2 = ax.plot(time_steps, actual_values, color="blue", linewidth=2, 
                    label="实际值", alpha=0.8)
    
    # 设置坐标轴刻度线在内侧
    ax.tick_params(axis='both', which='major', direction='in', length=6, width=1)
    ax.tick_params(axis='both', which='minor', direction='in', length=3, width=0.5)
    
    # 确保显示起始值 - 设置x轴从0开始
    ax.set_xlim(0, len(predicted_values) - 1)
    
    # 设置y轴范围，确保显示所有数据点
    all_values = list(predicted_values) + list(actual_values)
    y_min, y_max = min(all_values), max(all_values)
    y_range = y_max - y_min
    ax.set_ylim(y_min - 0.05 * y_range, y_max + 0.05 * y_range)
    
    # 设置网格
    ax.grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
    
    # 获取中文字体属性，使用更大字体提高清晰度
    label_font = get_chinese_font_prop(size=12, weight='bold')
    legend_font = get_chinese_font_prop(size=11, weight='normal')

    # 设置标签 - 使用更大字体提高清晰度
    ax.set_xlabel("时间步", fontproperties=label_font)
    ax.set_ylabel("客流量", fontproperties=label_font)

    # 隐藏标题 - 不显示图表标题
    # if title:
    #     title_font = get_chinese_font_prop(size=14, weight='bold')
    #     ax.set_title(title, fontproperties=title_font, pad=20)
    # elif station_id is not None:
    #     title_font = get_chinese_font_prop(size=14, weight='bold')
    #     ax.set_title(f"站点{station_id}客流量预测结果", fontproperties=title_font, pad=20)

    # 设置图例 - 放在右上角，避免压图线，使用清晰字体
    legend = ax.legend(loc='upper right', frameon=True, fancybox=True,
                      shadow=True, framealpha=0.95, prop=legend_font)

    # 优化图例外观
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_edgecolor('gray')
    legend.get_frame().set_linewidth(1.0)

    # 设置刻度标签字体
    tick_font = get_chinese_font_prop(size=10)
    for label in ax.get_xticklabels() + ax.get_yticklabels():
        label.set_fontproperties(tick_font)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_edgecolor('gray')
    legend.get_frame().set_linewidth(0.5)
    
    # 调整布局，确保图例不被裁剪
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        # 确保保存目录存在
        save_dir = os.path.dirname(save_path)
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir)
        plt.savefig(save_path, dpi=dpi, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
    
    # 显示图表
    if show_plot:
        plt.show()
    else:
        plt.close()

def create_peak_analysis_plot(predicted_values, actual_values, peaks_pred_time, peaks_pred_val,
                             peaks_true_time, peaks_true_val, valleys_pred_time=None, 
                             valleys_pred_val=None, valleys_true_time=None, valleys_true_val=None,
                             station_id=None, title=None, save_path=None, show_plot=False,
                             figsize=(14, 7), dpi=300):
    """
    创建包含峰值分析的改进绘图
    
    参数:
    - predicted_values: 预测值列表
    - actual_values: 实际值列表
    - peaks_pred_time, peaks_pred_val: 预测峰值的时间和值
    - peaks_true_time, peaks_true_val: 真实峰值的时间和值
    - valleys_*: 低谷相关参数（可选）
    - 其他参数同create_improved_plot
    """
    
    # 配置中文字体
    configure_chinese_font()
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 创建时间轴
    time_steps = np.arange(len(predicted_values))
    
    # 绘制主要曲线
    ax.plot(time_steps, predicted_values, color="red", linewidth=2, 
            label="预测值", alpha=0.8)
    ax.plot(time_steps, actual_values, color="blue", linewidth=2, 
            label="实际值", alpha=0.8)
    
    # 绘制峰值点
    ax.scatter(peaks_pred_time, peaks_pred_val, color="darkred", s=100, 
              marker="^", label="峰值预测", zorder=5)
    ax.scatter(peaks_true_time, peaks_true_val, color="darkblue", s=100, 
              marker="^", label="峰值真实", zorder=5)
    
    # 绘制低谷点（如果提供）
    if valleys_pred_time is not None and valleys_pred_val is not None:
        ax.scatter(valleys_pred_time, valleys_pred_val, color="orange", s=100, 
                  marker="v", label="低谷预测", zorder=5)
    if valleys_true_time is not None and valleys_true_val is not None:
        ax.scatter(valleys_true_time, valleys_true_val, color="green", s=100, 
                  marker="v", label="低谷真实", zorder=5)
    
    # 设置坐标轴刻度线在内侧
    ax.tick_params(axis='both', which='major', direction='in', length=6, width=1)
    ax.tick_params(axis='both', which='minor', direction='in', length=3, width=0.5)
    
    # 设置坐标轴范围
    ax.set_xlim(0, len(predicted_values) - 1)
    
    all_values = list(predicted_values) + list(actual_values)
    y_min, y_max = min(all_values), max(all_values)
    y_range = y_max - y_min
    ax.set_ylim(y_min - 0.05 * y_range, y_max + 0.05 * y_range)
    
    # 设置网格
    ax.grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
    
    # 获取中文字体属性，使用更大字体提高清晰度
    label_font = get_chinese_font_prop(size=12, weight='bold')
    legend_font = get_chinese_font_prop(size=10, weight='normal')

    # 设置标签 - 使用更大字体提高清晰度
    ax.set_xlabel("时间步", fontproperties=label_font)
    ax.set_ylabel("客流量", fontproperties=label_font)

    # 隐藏标题 - 不显示图表标题
    # if title:
    #     title_font = get_chinese_font_prop(size=14, weight='bold')
    #     ax.set_title(title, fontproperties=title_font, pad=20)
    # elif station_id is not None:
    #     title_font = get_chinese_font_prop(size=14, weight='bold')
    #     ax.set_title(f"站点{station_id}客流量预测结果与峰值分析", fontproperties=title_font, pad=20)

    # 设置图例 - 放在右上角外侧，避免压图线，使用清晰字体
    legend = ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', frameon=True,
                      fancybox=True, shadow=True, framealpha=0.95, prop=legend_font)

    # 优化图例外观
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_edgecolor('gray')
    legend.get_frame().set_linewidth(1.0)

    # 设置刻度标签字体
    tick_font = get_chinese_font_prop(size=10)
    for label in ax.get_xticklabels() + ax.get_yticklabels():
        label.set_fontproperties(tick_font)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_edgecolor('gray')
    legend.get_frame().set_linewidth(0.5)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        save_dir = os.path.dirname(save_path)
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir)
        plt.savefig(save_path, dpi=dpi, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
    
    # 显示图表
    if show_plot:
        plt.show()
    else:
        plt.close()

def create_multiple_stations_plot(x_data, y_data, station_ids, save_dir, 
                                 model_name="", figsize=(12, 6), dpi=300):
    """
    为多个站点创建改进的预测结果图表
    
    参数:
    - x_data: 预测值数据列表，每个元素对应一个站点
    - y_data: 实际值数据列表，每个元素对应一个站点
    - station_ids: 站点ID列表
    - save_dir: 保存目录
    - model_name: 模型名称（用于文件命名）
    - figsize: 图表尺寸
    - dpi: 图片分辨率
    """
    
    # 确保保存目录存在
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 为每个站点创建图表
    for i, station_id in enumerate(station_ids):
        if i < len(x_data) and i < len(y_data):
            save_path = os.path.join(save_dir, f'station_{station_id}_prediction.png')
            if model_name:
                save_path = os.path.join(save_dir, f'{model_name}_station_{station_id}_prediction.png')
            
            create_improved_plot(
                predicted_values=x_data[i],
                actual_values=y_data[i],
                station_id=station_id,
                save_path=save_path,
                show_plot=False,
                figsize=figsize,
                dpi=dpi
            )
    
    print(f"已为{len(station_ids)}个站点生成改进的预测结果图表，保存在: {save_dir}")

def create_high_clarity_multiple_stations_plot(x_data, y_data, station_ids, save_dir,
                                              model_name="", figsize=(14, 9), dpi=400):
    """
    为多个站点创建高清晰度的预测结果图表

    参数:
    - x_data: 预测值数据列表，每个元素对应一个站点
    - y_data: 实际值数据列表，每个元素对应一个站点
    - station_ids: 站点ID列表
    - save_dir: 保存目录
    - model_name: 模型名称（用于文件命名）
    - figsize: 图表尺寸（增大以提高清晰度）
    - dpi: 图片分辨率（提高到400）
    """

    # 确保保存目录存在
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # 为每个站点创建高清晰度图表
    for i, station_id in enumerate(station_ids):
        if i < len(x_data) and i < len(y_data):
            save_path = os.path.join(save_dir, f'station_{station_id}_prediction_hd.png')
            if model_name:
                save_path = os.path.join(save_dir, f'{model_name}_station_{station_id}_prediction_hd.png')

            create_high_clarity_plot(
                predicted_values=x_data[i],
                actual_values=y_data[i],
                station_id=station_id,
                save_path=save_path,
                show_plot=False,
                figsize=figsize,
                dpi=dpi
            )

    print(f"已为{len(station_ids)}个站点生成高清晰度预测结果图表，保存在: {save_dir}")

def create_high_clarity_plot(predicted_values, actual_values, station_id=None,
                            title=None, save_path=None, show_plot=False,
                            figsize=(12, 8), dpi=400):
    """
    创建高清晰度的预测结果绘图，专门优化中文字体显示

    参数:
    - predicted_values: 预测值列表
    - actual_values: 实际值列表
    - station_id: 站点ID
    - title: 图表标题
    - save_path: 保存路径
    - show_plot: 是否显示图表
    - figsize: 图表尺寸（增大以提高清晰度）
    - dpi: 图片分辨率（提高到400）
    """

    # 配置高清晰度字体
    configure_chinese_font()

    # 创建图表，使用更大尺寸
    fig, ax = plt.subplots(figsize=figsize, dpi=100)

    # 创建时间轴，从0开始显示起始值
    time_steps = np.arange(len(predicted_values))

    # 绘制预测值和实际值，使用更粗的线条
    line1 = ax.plot(time_steps, predicted_values, color="red", linewidth=2.5,
                    label="预测值", alpha=0.9)
    line2 = ax.plot(time_steps, actual_values, color="blue", linewidth=2.5,
                    label="实际值", alpha=0.9)

    # 设置坐标轴刻度线在内侧，使用更粗的线条
    ax.tick_params(axis='both', which='major', direction='in', length=8, width=1.5, labelsize=11)
    ax.tick_params(axis='both', which='minor', direction='in', length=4, width=1.0)

    # 确保显示起始值 - 设置x轴从0开始
    ax.set_xlim(0, len(predicted_values) - 1)

    # 设置y轴范围，确保显示所有数据点
    all_values = list(predicted_values) + list(actual_values)
    y_min, y_max = min(all_values), max(all_values)
    y_range = y_max - y_min
    ax.set_ylim(y_min - 0.05 * y_range, y_max + 0.05 * y_range)

    # 设置网格，使用更清晰的样式
    ax.grid(True, linestyle='--', alpha=0.4, linewidth=0.8)

    # 获取高清晰度中文字体
    label_font = get_chinese_font_prop(size=14, weight='bold')
    legend_font = get_chinese_font_prop(size=12, weight='normal')
    tick_font = get_chinese_font_prop(size=11)

    # 设置标签 - 使用大字体
    ax.set_xlabel("时间步", fontproperties=label_font, labelpad=10)
    ax.set_ylabel("客流量", fontproperties=label_font, labelpad=10)

    # 隐藏标题
    # if title:
    #     title_font = get_chinese_font_prop(size=16, weight='bold')
    #     ax.set_title(title, fontproperties=title_font, pad=25)

    # 设置图例 - 使用大字体和清晰样式
    legend = ax.legend(loc='upper right', frameon=True, fancybox=True,
                      shadow=True, framealpha=0.98, prop=legend_font,
                      edgecolor='black', facecolor='white')
    legend.get_frame().set_linewidth(1.2)

    # 设置刻度标签字体
    for label in ax.get_xticklabels() + ax.get_yticklabels():
        label.set_fontproperties(tick_font)

    # 调整布局，确保图例不被裁剪
    plt.tight_layout(pad=2.0)

    # 保存图表
    if save_path:
        # 确保保存目录存在
        save_dir = os.path.dirname(save_path)
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir)
        plt.savefig(save_path, dpi=dpi, bbox_inches='tight',
                   facecolor='white', edgecolor='none',
                   format='png', optimize=True)

    # 显示图表
    if show_plot:
        plt.show()
    else:
        plt.close()

def create_high_clarity_peak_analysis_plot(predicted_values, actual_values, peaks_pred_time, peaks_pred_val,
                                          peaks_true_time, peaks_true_val, valleys_pred_time=None,
                                          valleys_pred_val=None, valleys_true_time=None, valleys_true_val=None,
                                          station_id=None, title=None, save_path=None, show_plot=False,
                                          figsize=(16, 10), dpi=400):
    """
    创建高清晰度的峰值分析绘图

    参数:
    - predicted_values: 预测值列表
    - actual_values: 实际值列表
    - peaks_pred_time, peaks_pred_val: 预测峰值的时间和值
    - peaks_true_time, peaks_true_val: 真实峰值的时间和值
    - valleys_*: 低谷相关参数（可选）
    - 其他参数同create_high_clarity_plot
    """

    # 配置高清晰度字体
    configure_chinese_font()

    # 创建图表，使用更大尺寸
    fig, ax = plt.subplots(figsize=figsize, dpi=100)

    # 创建时间轴
    time_steps = np.arange(len(predicted_values))

    # 绘制主要曲线，使用更粗的线条
    ax.plot(time_steps, predicted_values, color="red", linewidth=3,
            label="预测值", alpha=0.9)
    ax.plot(time_steps, actual_values, color="blue", linewidth=3,
            label="实际值", alpha=0.9)

    # 绘制峰值点，使用更大的标记
    ax.scatter(peaks_pred_time, peaks_pred_val, color="darkred", s=150,
              marker="^", label="峰值预测", zorder=5, edgecolors='black', linewidth=1)
    ax.scatter(peaks_true_time, peaks_true_val, color="darkblue", s=150,
              marker="^", label="峰值真实", zorder=5, edgecolors='black', linewidth=1)

    # 绘制低谷点（如果提供），使用更大的标记
    if valleys_pred_time is not None and valleys_pred_val is not None:
        ax.scatter(valleys_pred_time, valleys_pred_val, color="orange", s=150,
                  marker="v", label="低谷预测", zorder=5, edgecolors='black', linewidth=1)
    if valleys_true_time is not None and valleys_true_val is not None:
        ax.scatter(valleys_true_time, valleys_true_val, color="green", s=150,
                  marker="v", label="低谷真实", zorder=5, edgecolors='black', linewidth=1)

    # 设置坐标轴刻度线在内侧，使用更粗的线条
    ax.tick_params(axis='both', which='major', direction='in', length=10, width=2, labelsize=12)
    ax.tick_params(axis='both', which='minor', direction='in', length=5, width=1.5)

    # 设置坐标轴范围
    ax.set_xlim(0, len(predicted_values) - 1)

    all_values = list(predicted_values) + list(actual_values)
    y_min, y_max = min(all_values), max(all_values)
    y_range = y_max - y_min
    ax.set_ylim(y_min - 0.05 * y_range, y_max + 0.05 * y_range)

    # 设置网格，使用更清晰的样式
    ax.grid(True, linestyle='--', alpha=0.4, linewidth=1.0)

    # 获取高清晰度中文字体
    label_font = get_chinese_font_prop(size=16, weight='bold')
    legend_font = get_chinese_font_prop(size=12, weight='normal')
    tick_font = get_chinese_font_prop(size=12)

    # 设置标签 - 使用大字体
    ax.set_xlabel("时间步", fontproperties=label_font, labelpad=15)
    ax.set_ylabel("客流量", fontproperties=label_font, labelpad=15)

    # 隐藏标题
    # if title:
    #     title_font = get_chinese_font_prop(size=18, weight='bold')
    #     ax.set_title(title, fontproperties=title_font, pad=30)

    # 设置图例 - 放在右上角外侧，使用大字体和清晰样式
    legend = ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', frameon=True,
                      fancybox=True, shadow=True, framealpha=0.98, prop=legend_font,
                      edgecolor='black', facecolor='white')
    legend.get_frame().set_linewidth(1.5)

    # 设置刻度标签字体
    for label in ax.get_xticklabels() + ax.get_yticklabels():
        label.set_fontproperties(tick_font)

    # 调整布局
    plt.tight_layout(pad=3.0)

    # 保存图表
    if save_path:
        save_dir = os.path.dirname(save_path)
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir)
        plt.savefig(save_path, dpi=dpi, bbox_inches='tight',
                   facecolor='white', edgecolor='none',
                   format='png', optimize=True)

    # 显示图表
    if show_plot:
        plt.show()
    else:
        plt.close()
