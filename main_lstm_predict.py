import numpy as np
import os
import time
import torch
from torch import nn
from torch.utils.tensorboard import SummaryWriter
from model.LSTM_model import LSTMModel
import matplotlib.pyplot as plt
# 添加中文字体支持
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
from utils.metrics import Metrics, Metrics_1d
from data.get_dataloader import get_inflow_dataloader, get_outflow_dataloader

# 设置设备
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 超参数设置
epoch_num = 200
lr = 0.001
time_interval = 15
time_lag = 10
tg_in_one_day = 72
forecast_day_number = 5
pre_len = 1
batch_size = 32
station_num = 276
model_type = 'lstm'
TIMESTAMP = str(time.strftime("%Y_%m_%d_%H_%M_%S"))
save_dir = './save_model/' + model_type + '_' + TIMESTAMP
if not os.path.exists(save_dir):
    os.makedirs(save_dir)

# 加载数据
print("加载数据...")
inflow_data_loader_train, inflow_data_loader_val, inflow_data_loader_test, max_inflow, min_inflow = \
    get_inflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day,
                          forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=batch_size)
outflow_data_loader_train, outflow_data_loader_val, outflow_data_loader_test, max_outflow, min_outflow = \
    get_outflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day,
                           forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=batch_size)

# 初始化模型
print("初始化LSTM模型...")
model = LSTMModel(time_lag, pre_len, station_num, device, hidden_size=128, num_layers=2)
model = model.to(device)

# 定义损失函数和优化器
criterion = nn.MSELoss().to(device)
optimizer = torch.optim.Adam(model.parameters(), lr=lr)

# TensorBoard
writer = SummaryWriter(log_dir=f'./logs/lstm_{TIMESTAMP}')

# 训练模型
print("开始训练LSTM模型...")
best_val_loss = float('inf')
patience = 10
patience_counter = 0
global_step = 0

for epoch in range(epoch_num):
    # 训练阶段
    model.train()
    train_loss = 0
    
    # 数据加载循环
    for i_batch, (inflow_batch, outflow_batch) in enumerate(zip(inflow_data_loader_train, outflow_data_loader_train)):
        # 解包数据
        train_inflow_X, train_inflow_Y = inflow_batch
        train_outflow_X, train_outflow_Y = outflow_batch
        
        # 准备数据
        train_inflow_X = train_inflow_X.type(torch.float32).to(device)
        train_inflow_Y = train_inflow_Y.type(torch.float32).to(device)
        train_outflow_X = train_outflow_X.type(torch.float32).to(device)
        
        # 前向传播
        optimizer.zero_grad()
        outputs = model(train_inflow_X, train_outflow_X)
        loss = criterion(outputs, train_inflow_Y)
        
        # 反向传播和优化
        loss.backward()
        optimizer.step()
        
        train_loss += loss.item()
        global_step += 1
        
        if i_batch % 10 == 0:
            print(f'Epoch [{epoch+1}/{epoch_num}], Batch [{i_batch+1}/{len(inflow_data_loader_train)}], Loss: {loss.item():.6f}')
    
    avg_train_loss = train_loss / len(inflow_data_loader_train)
    writer.add_scalar('Loss/train', avg_train_loss, epoch)
    
    # 验证阶段
    model.eval()
    val_loss = 0
    with torch.no_grad():
        for i_batch, (inflow_batch, outflow_batch) in enumerate(zip(inflow_data_loader_val, outflow_data_loader_val)):
            # 解包数据
            val_inflow_X, val_inflow_Y = inflow_batch
            val_outflow_X, val_outflow_Y = outflow_batch
            
            # 准备数据
            val_inflow_X = val_inflow_X.type(torch.float32).to(device)
            val_inflow_Y = val_inflow_Y.type(torch.float32).to(device)
            val_outflow_X = val_outflow_X.type(torch.float32).to(device)
            
            # 前向传播
            outputs = model(val_inflow_X, val_outflow_X)
            loss = criterion(outputs, val_inflow_Y)
            
            val_loss += loss.item()
    
    avg_val_loss = val_loss / len(inflow_data_loader_val)
    writer.add_scalar('Loss/validation', avg_val_loss, epoch)
    
    print(f'Epoch [{epoch+1}/{epoch_num}], Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}')
    
    # 保存最佳模型
    if avg_val_loss < best_val_loss:
        best_val_loss = avg_val_loss
        patience_counter = 0
        torch.save(model.state_dict(), f'{save_dir}/best_model.pth')
        print(f"保存最佳模型，验证损失: {best_val_loss:.6f}")
    else:
        patience_counter += 1
        
    # 早停
    if patience_counter >= patience:
        print(f"早停: {patience}轮验证损失未改善")
        break
    
    # 每10轮保存一次检查点
    if (epoch + 1) % 10 == 0:
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'loss': avg_train_loss,
        }, f'{save_dir}/checkpoint_epoch_{epoch+1}.pth')

# 加载最佳模型进行测试
print("加载最佳模型进行测试...")
model.load_state_dict(torch.load(f'{save_dir}/best_model.pth'))
model.eval()

# 测试
result = []
result_original = []
if not os.path.exists('result/lstm_prediction'):
    os.makedirs('result/lstm_prediction/')
if not os.path.exists('result/lstm_original'):
    os.makedirs('result/lstm_original')

# 测试
with torch.no_grad():
    test_loss = 0
    # 测试数据加载循环
    for inflow_batch, outflow_batch in zip(inflow_data_loader_test, outflow_data_loader_test):
        # 检查数据结构并正确解包
        if len(inflow_batch) == 3:  # 如果有3个元素
            test_inflow_X, test_inflow_Y, test_inflow_Y_original = inflow_batch
            test_outflow_X, test_outflow_Y, _ = outflow_batch
        else:  # 如果有2个元素
            test_inflow_X, test_inflow_Y = inflow_batch
            test_outflow_X, test_outflow_Y = outflow_batch
            # 创建原始标签
            test_inflow_Y_original = test_inflow_Y.clone()
        
        test_inflow_X = test_inflow_X.type(torch.float32).to(device)
        test_inflow_Y = test_inflow_Y.type(torch.float32).to(device)
        test_outflow_X = test_outflow_X.type(torch.float32).to(device)
        
        # 预测
        target = model(test_inflow_X, test_outflow_X)
        
        # 计算损失
        loss = criterion(target, test_inflow_Y)
        test_loss += loss.item()
        
        # 转换回原始尺度
        clone_prediction = target.cpu().detach().numpy().copy() * max_inflow
        for i in range(clone_prediction.shape[0]):
            result.append(clone_prediction[i])
        
        # 获取原始标签
        if 'test_inflow_Y_original' in locals():
            # 如果有原始标签，直接使用
            y_original = test_inflow_Y_original.cpu().detach().numpy()
            if y_original.max() <= 1:  # 如果是归一化的数据
                y_original = y_original * max_inflow
        else:
            # 否则使用缩放后的预测标签
            y_original = test_inflow_Y.cpu().detach().numpy() * max_inflow
            
        for i in range(y_original.shape[0]):
            result_original.append(y_original[i])
    
    # 处理结果
    print(f"结果形状: {np.array(result).shape}, 原始形状: {np.array(result_original).shape}")
    
    # 取整并处理负值
    result = np.array(result).astype(np.int64)
    result[result < 0] = 0
    result_original = np.array(result_original).astype(np.int64)
    result_original[result_original < 0] = 0
    
    # 选择特定站点进行可视化
    x = [[], [], [], [], []]
    y = [[], [], [], [], []]
    for i in range(result.shape[0]):
        x[0].append(result[i][4][0])
        y[0].append(result_original[i][4][0])
        x[1].append(result[i][18][0])
        y[1].append(result_original[i][18][0])
        x[2].append(result[i][30][0])
        y[2].append(result_original[i][30][0])
        x[3].append(result[i][60][0])
        y[3].append(result_original[i][60][0])
        x[4].append(result[i][94][0])
        y[4].append(result_original[i][94][0])
    
    # 重塑结果用于评估
    result = np.array(result).reshape(station_num, -1)
    result_original = result_original.reshape(station_num, -1)
    
    # 计算整体评估指标
    RMSE, R2, MAE, WMAPE = Metrics(result_original, result).evaluate_performance()
    
    # 计算平均测试损失
    avg_test_loss = test_loss / len(inflow_data_loader_test)
    print(f'测试损失: {avg_test_loss:.6f}')
    
    # 打印评估指标
    print("\n========== LSTM模型预测结果评估指标 ==========")
    print(f"RMSE (均方根误差): {RMSE:.4f}")
    print(f"R2 (决定系数): {R2:.4f}")
    print(f"MAE (平均绝对误差): {MAE:.4f}")
    print(f"WMAPE (加权平均绝对百分比误差): {WMAPE:.4f}")
    print("=====================================\n")
    
    # 计算各站点评估指标
    RMSE_y0, R2_y0, MAE_y0, WMAPE_y0 = Metrics_1d(y[0], x[0]).evaluate_performance()
    RMSE_y1, R2_y1, MAE_y1, WMAPE_y1 = Metrics_1d(y[1], x[1]).evaluate_performance()
    RMSE_y2, R2_y2, MAE_y2, WMAPE_y2 = Metrics_1d(y[2], x[2]).evaluate_performance()
    RMSE_y3, R2_y3, MAE_y3, WMAPE_y3 = Metrics_1d(y[3], x[3]).evaluate_performance()
    RMSE_y4, R2_y4, MAE_y4, WMAPE_y4 = Metrics_1d(y[4], x[4]).evaluate_performance()
    
    # 打印各站点评估指标
    print("\n========== 各站点预测结果评估指标 ==========")
    print(f"站点 4 - RMSE: {RMSE_y0:.4f}, R2: {R2_y0:.4f}, MAE: {MAE_y0:.4f}, WMAPE: {WMAPE_y0:.4f}")
    print(f"站点 18 - RMSE: {RMSE_y1:.4f}, R2: {R2_y1:.4f}, MAE: {MAE_y1:.4f}, WMAPE: {WMAPE_y1:.4f}")
    print(f"站点 30 - RMSE: {RMSE_y2:.4f}, R2: {R2_y2:.4f}, MAE: {MAE_y2:.4f}, WMAPE: {WMAPE_y2:.4f}")
    print(f"站点 60 - RMSE: {RMSE_y3:.4f}, R2: {R2_y3:.4f}, MAE: {MAE_y3:.4f}, WMAPE: {WMAPE_y3:.4f}")
    print(f"站点 94 - RMSE: {RMSE_y4:.4f}, R2: {R2_y4:.4f}, MAE: {MAE_y4:.4f}, WMAPE: {WMAPE_y4:.4f}")
    print("===========================================\n")
    
    # 保存评估指标
    ALL = [RMSE, MAE, WMAPE]
    y0_ALL = [RMSE_y0, MAE_y0, WMAPE_y0]
    y1_ALL = [RMSE_y1, MAE_y1, WMAPE_y1]
    y2_ALL = [RMSE_y2, MAE_y2, WMAPE_y2]
    y3_ALL = [RMSE_y3, MAE_y3, WMAPE_y3]
    y4_ALL = [RMSE_y4, MAE_y4, WMAPE_y4]
    
    # 创建结果图表目录
    if not os.path.exists('result/lstm'):
        os.makedirs('result/lstm')
    
    # 保存评估指标
    np.savetxt(f'result/lstm/lstm_ALL.txt', ALL)
    np.savetxt(f'result/lstm/lstm_y0_ALL.txt', y0_ALL)
    np.savetxt(f'result/lstm/lstm_y1_ALL.txt', y1_ALL)
    np.savetxt(f'result/lstm/lstm_y2_ALL.txt', y2_ALL)
    np.savetxt(f'result/lstm/lstm_y3_ALL.txt', y3_ALL)
    np.savetxt(f'result/lstm/lstm_y4_ALL.txt', y4_ALL)
    
    # 打印评估指标
    print("整体评估指标:", ALL)
    print("站点4评估指标:", y0_ALL)
    print("站点18评估指标:", y1_ALL)
    print("站点30评估指标:", y2_ALL)
    print("站点60评估指标:", y3_ALL)
    print("站点94评估指标:", y4_ALL)
    
    # 创建结果图表目录
    if not os.path.exists('result/lstm/figures'):
        os.makedirs('result/lstm/figures')
    
    # 导入改进的绘图工具
    from utils.plot_utils import create_improved_plot, create_multiple_stations_plot

    # 使用改进的绘图函数为多个站点创建图表
    station_ids = [4, 18, 30, 60, 94]
    create_multiple_stations_plot(
        x_data=x,
        y_data=y,
        station_ids=station_ids,
        save_dir='result/lstm/figures',
        model_name="lstm"
    )
    
    # 可视化站点4的预测结果（保留原有的显示代码）
    plt.figure(figsize=(12, 6))
    plt.plot(x[0], color="r", label="预测值")
    plt.plot(y[0], color="b", label="实际值")
    plt.legend(loc='best')
    plt.title("站点4客流量预测结果")
    plt.xlabel("时间步")
    plt.ylabel("客流量")
    plt.savefig(f'result/lstm/station_4_prediction.png', dpi=300)
    plt.show()

print("LSTM模型训练和评估完成！")