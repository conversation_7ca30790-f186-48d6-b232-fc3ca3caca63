#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有高清晰度绘图功能
"""

import numpy as np
import os
from utils.plot_utils import (create_high_clarity_plot, create_high_clarity_peak_analysis_plot,
                             create_high_clarity_multiple_stations_plot)

def test_all_high_clarity_functions():
    """测试所有高清晰度绘图功能"""
    print("测试所有高清晰度绘图功能...")
    
    # 创建测试数据
    np.random.seed(42)
    time_steps = 100
    
    # 生成多个站点的测试数据
    station_ids = [4, 18, 30, 60, 94]
    x_data = []  # 预测值
    y_data = []  # 实际值
    
    for i, station_id in enumerate(station_ids):
        # 为每个站点生成不同的数据
        base_flow = 40 + 20 * np.sin(np.linspace(0, 3*np.pi + i*0.5, time_steps))
        noise_pred = np.random.normal(0, 4, time_steps)
        noise_true = np.random.normal(0, 2, time_steps)
        
        pred_station = np.maximum(base_flow + noise_pred, 0)
        true_station = np.maximum(base_flow + noise_true, 0)
        
        x_data.append(pred_station)
        y_data.append(true_station)
    
    # 创建测试结果目录
    test_dirs = [
        'test_results/high_clarity',
        'test_results/high_clarity/basic',
        'test_results/high_clarity/multiple',
        'test_results/high_clarity/peaks'
    ]
    
    for test_dir in test_dirs:
        if not os.path.exists(test_dir):
            os.makedirs(test_dir)
    
    print("1. 测试高清晰度基本绘图...")
    
    # 测试基本高清晰度绘图
    try:
        create_high_clarity_plot(
            predicted_values=x_data[0],
            actual_values=y_data[0],
            station_id=4,
            title=None,
            save_path='test_results/high_clarity/basic/station_4_hd.png',
            show_plot=False,
            figsize=(14, 9),
            dpi=400
        )
        print("   ✓ 高清晰度基本绘图测试成功")
    except Exception as e:
        print(f"   ✗ 高清晰度基本绘图测试失败: {e}")
        return False
    
    print("2. 测试高清晰度多站点绘图...")
    
    # 测试多站点高清晰度绘图
    try:
        create_high_clarity_multiple_stations_plot(
            x_data=x_data,
            y_data=y_data,
            station_ids=station_ids,
            save_dir='test_results/high_clarity/multiple',
            model_name="test_hd",
            figsize=(14, 9),
            dpi=400
        )
        print("   ✓ 高清晰度多站点绘图测试成功")
    except Exception as e:
        print(f"   ✗ 高清晰度多站点绘图测试失败: {e}")
        return False
    
    print("3. 测试高清晰度峰值分析绘图...")
    
    # 生成峰值和低谷数据
    predicted_values = x_data[0]
    actual_values = y_data[0]
    
    peaks_indices = [20, 45, 70, 95]
    valleys_indices = [10, 35, 60, 85]
    
    peaks_pred_time = peaks_indices
    peaks_pred_val = [predicted_values[i] for i in peaks_indices]
    peaks_true_time = peaks_indices
    peaks_true_val = [actual_values[i] for i in peaks_indices]
    
    valleys_pred_time = valleys_indices
    valleys_pred_val = [predicted_values[i] for i in valleys_indices]
    valleys_true_time = valleys_indices
    valleys_true_val = [actual_values[i] for i in valleys_indices]
    
    # 测试峰值分析高清晰度绘图
    try:
        create_high_clarity_peak_analysis_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            peaks_pred_time=peaks_pred_time,
            peaks_pred_val=peaks_pred_val,
            peaks_true_time=peaks_true_time,
            peaks_true_val=peaks_true_val,
            valleys_pred_time=valleys_pred_time,
            valleys_pred_val=valleys_pred_val,
            valleys_true_time=valleys_true_time,
            valleys_true_val=valleys_true_val,
            station_id=4,
            title=None,
            save_path='test_results/high_clarity/peaks/station_4_peaks_hd.png',
            show_plot=False,
            figsize=(16, 10),
            dpi=400
        )
        print("   ✓ 高清晰度峰值分析绘图测试成功")
    except Exception as e:
        print(f"   ✗ 高清晰度峰值分析绘图测试失败: {e}")
        return False
    
    return True

def create_comparison_plots():
    """创建标准版本与高清晰度版本的对比"""
    print("\n创建标准版本与高清晰度版本对比...")
    
    from utils.plot_utils import create_improved_plot, create_peak_analysis_plot
    
    # 创建测试数据
    np.random.seed(123)
    time_steps = 80
    predicted_values = 50 + 20 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    actual_values = 50 + 20 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 创建对比目录
    if not os.path.exists('test_results/comparison'):
        os.makedirs('test_results/comparison')
    
    try:
        # 标准版本
        create_improved_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            station_id=4,
            title=None,
            save_path='test_results/comparison/standard_version.png',
            show_plot=False
        )
        
        # 高清晰度版本
        create_high_clarity_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            station_id=4,
            title=None,
            save_path='test_results/comparison/high_clarity_version.png',
            show_plot=False,
            figsize=(14, 9),
            dpi=400
        )
        
        print("   ✓ 版本对比图创建成功")
        return True
        
    except Exception as e:
        print(f"   ✗ 版本对比图创建失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("高清晰度绘图功能全面测试")
    print("=" * 70)
    
    # 运行测试
    test1_result = test_all_high_clarity_functions()
    test2_result = create_comparison_plots()
    
    print("\n" + "=" * 70)
    if test1_result and test2_result:
        print("✓ 所有高清晰度绘图功能测试通过！")
        print("\n测试结果保存在以下目录:")
        print("- test_results/high_clarity/basic/ - 基本高清晰度绘图")
        print("- test_results/high_clarity/multiple/ - 多站点高清晰度绘图")
        print("- test_results/high_clarity/peaks/ - 峰值分析高清晰度绘图")
        print("- test_results/comparison/ - 标准版本与高清晰度版本对比")
        
        print("\n高清晰度版本特性:")
        print("✓ 图表尺寸: 14×9英寸 (基本) / 16×10英寸 (峰值分析)")
        print("✓ 分辨率: 400 DPI")
        print("✓ 字体大小: 标签16号，图例12号，刻度12号")
        print("✓ 线条粗细: 3.0像素")
        print("✓ 标记大小: 150像素，带黑色边框")
        print("✓ 网格透明度: 40%，线宽1.0像素")
        print("✓ 图例透明度: 98%，带黑色边框")
        
    else:
        print("✗ 部分测试失败，请检查错误信息")
    
    print("=" * 70)
