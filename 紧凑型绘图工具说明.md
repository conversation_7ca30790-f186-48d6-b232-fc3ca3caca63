# 紧凑型绘图工具说明

## 概述

`compact_plot_utils.py` 是专门为生成小尺寸、高质量图片而设计的绘图工具模块。主要特点：

- **图片宽度**: 控制在6cm以内
- **标签字体**: 6号宋体
- **分辨率**: 300 DPI
- **适用场景**: 论文插图、小尺寸展示

## 主要功能

### 1. 基础函数

- `configure_compact_font()`: 配置紧凑型绘图的中文字体
- `get_compact_font_prop()`: 获取紧凑型绘图的字体属性
- `safe_savefig_compact()`: 安全的图片保存函数

### 2. 绘图函数

- `create_compact_plot()`: 创建紧凑型基本预测结果图表
- `create_compact_peak_analysis_plot()`: 创建紧凑型峰值分析图表
- `create_compact_multiple_stations_plot()`: 批量创建紧凑型多站点图表

## 技术规格

### 字体设置
```
标签字体: 6号宋体，粗体
图例字体: 5号宋体，正常
刻度字体: 4号宋体，正常
基础字体: 6号宋体，正常
```

### 图表规格
```
默认尺寸: 6×4cm
分辨率: 300 DPI
线条粗细: 1.0像素
标记大小: 15像素
网格线宽: 0.3像素
刻度长度: 3像素
```

### 布局设置
```
标签间距: 2像素
图表边距: 0.5像素
图例边框: 0.5像素线宽
网格透明度: 30%
图例透明度: 90%
```

## 使用示例

### 基本绘图
```python
from utils.compact_plot_utils import create_compact_plot

create_compact_plot(
    predicted_values=predicted_data,
    actual_values=actual_data,
    station_id=4,
    save_path='result/compact_station_4.png',
    width_cm=6,
    height_cm=4,
    dpi=300
)
```

### 峰值分析绘图
```python
from utils.compact_plot_utils import create_compact_peak_analysis_plot

create_compact_peak_analysis_plot(
    predicted_values=predicted_data,
    actual_values=actual_data,
    peaks_pred_time=peak_times,
    peaks_pred_val=peak_values,
    peaks_true_time=true_peak_times,
    peaks_true_val=true_peak_values,
    valleys_pred_time=valley_times,
    valleys_pred_val=valley_values,
    valleys_true_time=true_valley_times,
    valleys_true_val=true_valley_values,
    station_id=4,
    save_path='result/compact_peaks.png',
    width_cm=6,
    height_cm=4,
    dpi=300
)
```

### 多站点批量绘图
```python
from utils.compact_plot_utils import create_compact_multiple_stations_plot

create_compact_multiple_stations_plot(
    x_data=all_predictions,
    y_data=all_actual_values,
    station_ids=[4, 18, 30, 60, 94],
    save_dir='result/compact_figures',
    model_name="compact_model",
    width_cm=6,
    height_cm=4,
    dpi=300
)
```

## 与标准版本对比

| 特性 | 紧凑型版本 | 标准版本 | 高清版本 |
|------|------------|----------|----------|
| **图表尺寸** | 6×4cm | 12×8cm | 16×10cm |
| **标签字体** | 6号 | 18号 | 22号 |
| **图例字体** | 5号 | 14号 | 16号 |
| **刻度字体** | 4号 | 12号 | 15号 |
| **分辨率** | 300 DPI | 300 DPI | 400 DPI |
| **线条粗细** | 1.0像素 | 2.5像素 | 3.5像素 |
| **文件大小** | 50-100KB | 200-400KB | 500-800KB |

## 适用场景

### 推荐使用场景
- 📄 **学术论文插图**: 期刊论文中的小图表
- 📊 **技术报告**: 需要节省空间的报告
- 📱 **网页展示**: 网页中的小尺寸图表
- 📧 **邮件附件**: 文件大小受限的场景
- 📋 **多图表展示**: 需要在一页中放置多个图表

### 不推荐使用场景
- 🖥️ **大屏幕演示**: 字体太小，观众看不清
- 👥 **会议报告**: 远距离观看困难
- 📊 **海报展示**: 尺寸太小，不够醒目

## 测试验证

运行测试脚本验证功能：
```bash
python test_compact_plot.py
```

测试内容包括：
- ✅ 紧凑型基本绘图测试
- ✅ 紧凑型峰值分析绘图测试
- ✅ 紧凑型多站点绘图测试
- ✅ 不同尺寸测试
- ✅ 与其他版本对比测试

## extract_station_peaks.py 修改

已修改 `extract_station_peaks.py` 文件使用紧凑型绘图：

```python
# 导入紧凑型绘图工具
from utils.compact_plot_utils import create_compact_peak_analysis_plot

# 使用紧凑型峰值分析绘图
create_compact_peak_analysis_plot(
    predicted_values=station_pred,
    actual_values=station_true,
    peaks_pred_time=top_peaks_time,
    peaks_pred_val=top_peaks_pred,
    peaks_true_time=top_peaks_time,
    peaks_true_val=top_peaks_true,
    valleys_pred_time=bottom_valleys_time,
    valleys_pred_val=bottom_valleys_pred,
    valleys_true_time=bottom_valleys_time,
    valleys_true_val=bottom_valleys_true,
    station_id=real_station_id,
    title=None,
    save_path=f'result/peaks_analysis/compact_station_{real_station_id}_peaks_valleys.png',
    show_plot=False,
    width_cm=6,
    height_cm=4,
    dpi=300
)
```

## 文件命名规则

紧凑型图表文件名前缀为 `compact_`：
- 基本图表: `compact_station_{id}_prediction.png`
- 峰值分析: `compact_station_{id}_peaks_valleys.png`
- 模型图表: `compact_{model}_station_{id}_prediction.png`

## 质量保证

### 字体清晰度
- 使用宋体确保中文字符清晰
- 6号字体在300 DPI下保持可读性
- 粗体标签增强重要信息的可见性

### 图表布局
- 紧凑的间距设计，充分利用空间
- 图例放置在合适位置，不遮挡数据
- 网格线透明度适中，起到辅助作用

### 文件优化
- 300 DPI确保打印质量
- 文件大小控制在合理范围
- PNG格式保证图像质量

## 注意事项

1. **字体大小限制**: 6号字体是在6cm宽度下的最佳选择，更小可能影响可读性
2. **图表内容**: 适合数据点不太密集的图表
3. **颜色选择**: 使用对比度较高的颜色确保小尺寸下的可见性
4. **标记大小**: 15像素的标记在小图中仍然清晰可见
5. **图例位置**: 峰值分析图的图例放在图表外侧以避免遮挡

## 扩展功能

可以根据需要调整的参数：
- `width_cm`: 图表宽度（建议不超过6cm）
- `height_cm`: 图表高度（建议保持合适的宽高比）
- `dpi`: 分辨率（300 DPI适合大多数场景）
- 字体大小可以在函数内部微调
