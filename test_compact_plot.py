#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试紧凑型绘图工具
"""

import numpy as np
import os
from utils.compact_plot_utils import (configure_compact_font, get_compact_font_prop, 
                                     create_compact_plot, create_compact_peak_analysis_plot,
                                     create_compact_multiple_stations_plot)

def test_compact_basic_plot():
    """测试紧凑型基本绘图"""
    print("测试紧凑型基本绘图...")
    
    # 创建测试数据
    np.random.seed(42)
    time_steps = 50
    predicted_values = 45 + 20 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    actual_values = 45 + 20 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 创建测试目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    try:
        # 测试紧凑型绘图
        create_compact_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            station_id=4,
            title=None,
            save_path='test_results/compact_basic_test.png',
            show_plot=False,
            width_cm=6,
            height_cm=4,
            dpi=300
        )
        
        print("✓ 紧凑型基本绘图测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 紧凑型基本绘图测试失败: {e}")
        return False

def test_compact_peak_analysis():
    """测试紧凑型峰值分析绘图"""
    print("测试紧凑型峰值分析绘图...")
    
    # 创建测试数据
    np.random.seed(123)
    time_steps = 60
    predicted_values = 50 + 25 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
    actual_values = 50 + 25 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 生成峰值和低谷数据
    peaks_indices = [12, 30, 48]
    valleys_indices = [6, 24, 42]
    
    peaks_pred_time = peaks_indices
    peaks_pred_val = [predicted_values[i] for i in peaks_indices]
    peaks_true_time = peaks_indices
    peaks_true_val = [actual_values[i] for i in peaks_indices]
    
    valleys_pred_time = valleys_indices
    valleys_pred_val = [predicted_values[i] for i in valleys_indices]
    valleys_true_time = valleys_indices
    valleys_true_val = [actual_values[i] for i in valleys_indices]
    
    try:
        # 测试紧凑型峰值分析绘图
        create_compact_peak_analysis_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            peaks_pred_time=peaks_pred_time,
            peaks_pred_val=peaks_pred_val,
            peaks_true_time=peaks_true_time,
            peaks_true_val=peaks_true_val,
            valleys_pred_time=valleys_pred_time,
            valleys_pred_val=valleys_pred_val,
            valleys_true_time=valleys_true_time,
            valleys_true_val=valleys_true_val,
            station_id=4,
            title=None,
            save_path='test_results/compact_peak_analysis_test.png',
            show_plot=False,
            width_cm=6,
            height_cm=4,
            dpi=300
        )
        
        print("✓ 紧凑型峰值分析绘图测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 紧凑型峰值分析绘图测试失败: {e}")
        return False

def test_compact_multiple_stations():
    """测试紧凑型多站点绘图"""
    print("测试紧凑型多站点绘图...")
    
    # 创建多个站点的测试数据
    station_ids = [4, 18, 30]
    x_data = []  # 预测值
    y_data = []  # 实际值
    
    for i, station_id in enumerate(station_ids):
        np.random.seed(42 + i)
        time_steps = 40
        base_flow = 40 + 15 * np.sin(np.linspace(0, 2*np.pi + i*0.5, time_steps))
        noise_pred = np.random.normal(0, 3, time_steps)
        noise_true = np.random.normal(0, 2, time_steps)
        
        pred_station = np.maximum(base_flow + noise_pred, 0)
        true_station = np.maximum(base_flow + noise_true, 0)
        
        x_data.append(pred_station)
        y_data.append(true_station)
    
    try:
        # 测试紧凑型多站点绘图
        create_compact_multiple_stations_plot(
            x_data=x_data,
            y_data=y_data,
            station_ids=station_ids,
            save_dir='test_results/compact_multiple',
            model_name="test",
            width_cm=6,
            height_cm=4,
            dpi=300
        )
        
        print("✓ 紧凑型多站点绘图测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 紧凑型多站点绘图测试失败: {e}")
        return False

def test_different_sizes():
    """测试不同尺寸的紧凑型绘图"""
    print("测试不同尺寸的紧凑型绘图...")
    
    # 创建测试数据
    np.random.seed(456)
    time_steps = 30
    predicted_values = 40 + 15 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    actual_values = 40 + 15 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 1.5, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 测试不同尺寸
    sizes = [
        (4, 3, "4x3cm"),
        (5, 3.5, "5x3.5cm"),
        (6, 4, "6x4cm"),
        (6, 5, "6x5cm")
    ]
    
    success_count = 0
    
    for width, height, size_name in sizes:
        try:
            create_compact_plot(
                predicted_values=predicted_values,
                actual_values=actual_values,
                station_id=4,
                title=None,
                save_path=f'test_results/compact_size_{size_name.replace("x", "_").replace("cm", "")}.png',
                show_plot=False,
                width_cm=width,
                height_cm=height,
                dpi=300
            )
            print(f"  ✓ {size_name} 尺寸测试成功")
            success_count += 1
            
        except Exception as e:
            print(f"  ✗ {size_name} 尺寸测试失败: {e}")
    
    return success_count == len(sizes)

def create_size_comparison():
    """创建尺寸对比图"""
    print("创建尺寸对比图...")
    
    # 创建测试数据
    np.random.seed(789)
    time_steps = 35
    predicted_values = 35 + 18 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 2.5, time_steps)
    actual_values = 35 + 18 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 创建不同版本的对比
    versions = [
        ("紧凑型", 6, 4, 300, "compact"),
        ("标准型", 12, 8, 300, "standard"),
        ("高清型", 16, 10, 400, "hd")
    ]
    
    for version_name, width_cm, height_cm, dpi, suffix in versions:
        try:
            if version_name == "紧凑型":
                create_compact_plot(
                    predicted_values=predicted_values,
                    actual_values=actual_values,
                    station_id=4,
                    title=None,
                    save_path=f'test_results/comparison_{suffix}.png',
                    show_plot=False,
                    width_cm=width_cm,
                    height_cm=height_cm,
                    dpi=dpi
                )
            else:
                # 对于标准型和高清型，我们创建一个简单的对比图
                import matplotlib.pyplot as plt
                
                width_inch = width_cm / 2.54
                height_inch = height_cm / 2.54
                
                fig, ax = plt.subplots(figsize=(width_inch, height_inch), dpi=100)
                
                time_axis = np.arange(len(predicted_values))
                ax.plot(time_axis, predicted_values, color="red", linewidth=2, label="预测值", alpha=0.8)
                ax.plot(time_axis, actual_values, color="blue", linewidth=2, label="实际值", alpha=0.8)
                
                ax.set_xlabel("时间步", fontsize=12)
                ax.set_ylabel("客流量", fontsize=12)
                ax.legend(fontsize=10)
                ax.grid(True, alpha=0.3)
                
                plt.tight_layout()
                plt.savefig(f'test_results/comparison_{suffix}.png', dpi=dpi, bbox_inches='tight')
                plt.close()
            
            print(f"  ✓ {version_name} 对比图创建成功")
            
        except Exception as e:
            print(f"  ✗ {version_name} 对比图创建失败: {e}")

if __name__ == "__main__":
    import os
    
    # 创建测试结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    print("=" * 60)
    print("紧凑型绘图工具测试")
    print("=" * 60)
    
    # 运行测试
    test1 = test_compact_basic_plot()
    test2 = test_compact_peak_analysis()
    test3 = test_compact_multiple_stations()
    test4 = test_different_sizes()
    create_size_comparison()
    
    print("\n" + "=" * 60)
    if test1 and test2 and test3 and test4:
        print("✓ 所有紧凑型绘图测试通过！")
        print("紧凑型绘图工具工作正常")
    else:
        print("✗ 部分测试失败，需要进一步检查")
    print("=" * 60)
    
    print("\n紧凑型绘图规格:")
    print("1. 📏 图表宽度: 6cm以内 (默认6×4cm)")
    print("2. 📝 标签字体: 6号宋体，粗体")
    print("3. 📝 图例字体: 5号宋体，正常")
    print("4. 📝 刻度字体: 4号宋体，正常")
    print("5. 📊 分辨率: 300 DPI")
    print("6. 📐 线条粗细: 1.0像素")
    print("7. 🔍 标记大小: 15像素")
    print("8. 📋 网格线宽: 0.3像素")
    print("9. 🎯 适用场景: 论文插图，小尺寸展示")
    print("10. 测试结果保存在 test_results/ 目录下")
    
    print("\n文件大小对比:")
    print("• 紧凑型 (6×4cm, 300DPI): 约50-100KB")
    print("• 标准型 (12×8cm, 300DPI): 约200-400KB") 
    print("• 高清型 (16×10cm, 400DPI): 约500-800KB")
