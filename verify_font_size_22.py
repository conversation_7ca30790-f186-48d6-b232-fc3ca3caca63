#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证22号字体大小设置
"""

import numpy as np
import os

def verify_font_size_22_settings():
    """验证22号字体大小设置"""
    print("验证22号字体大小设置...")
    
    try:
        from utils.plot_utils import get_chinese_font_prop
        
        # 测试不同字体大小设置
        label_font = get_chinese_font_prop(size=22, weight='bold')
        legend_font = get_chinese_font_prop(size=16, weight='normal')
        tick_font = get_chinese_font_prop(size=15)
        
        print(f"✓ 标签字体设置: {label_font.get_size()}号, 粗细: {label_font.get_weight()}")
        print(f"✓ 图例字体设置: {legend_font.get_size()}号, 粗细: {legend_font.get_weight()}")
        print(f"✓ 刻度字体设置: {tick_font.get_size()}号")
        
        # 验证字体大小是否正确
        if label_font.get_size() == 22:
            print("✓ 标签字体大小正确：22号")
        else:
            print(f"✗ 标签字体大小错误：{label_font.get_size()}号，应该是22号")
            return False
            
        if legend_font.get_size() == 16:
            print("✓ 图例字体大小正确：16号")
        else:
            print(f"✗ 图例字体大小错误：{legend_font.get_size()}号，应该是16号")
            return False
            
        if tick_font.get_size() == 15:
            print("✓ 刻度字体大小正确：15号")
        else:
            print(f"✗ 刻度字体大小错误：{tick_font.get_size()}号，应该是15号")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 字体大小验证失败: {e}")
        return False

def create_quick_test_22():
    """创建快速测试图表"""
    print("创建22号字体快速测试图表...")
    
    try:
        from utils.plot_utils import create_high_clarity_plot
        
        # 创建简单测试数据
        np.random.seed(42)
        predicted_values = np.random.rand(30) * 45 + 35
        actual_values = np.random.rand(30) * 45 + 35
        
        # 创建测试目录
        if not os.path.exists('test_results'):
            os.makedirs('test_results')
        
        # 测试高清晰度绘图
        create_high_clarity_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            station_id=4,
            title=None,
            save_path='test_results/quick_font_22_test.png',
            show_plot=False,
            figsize=(16, 10),  # 增大图表尺寸
            dpi=400
        )
        
        print("✓ 22号字体快速测试图表创建成功")
        return True
        
    except Exception as e:
        print(f"✗ 22号字体快速测试图表创建失败: {e}")
        return False

def show_font_size_evolution():
    """显示字体大小演进"""
    print("\n" + "=" * 60)
    print("字体大小演进历程")
    print("=" * 60)
    
    evolution_data = [
        ("原始设置", 12, 10, 9),
        ("第一次调整", 14, 12, 11),
        ("第二次调整", 18, 14, 13),
        ("当前设置", 22, 16, 15)
    ]
    
    print("┌─────────────┬──────────┬──────────┬──────────┬──────────┐")
    print("│    阶段     │ 标签字体 │ 图例字体 │ 刻度字体 │   变化   │")
    print("├─────────────┼──────────┼──────────┼──────────┼──────────┤")
    
    for i, (stage, label, legend, tick) in enumerate(evolution_data):
        if i == 0:
            change = "基准"
        else:
            prev_label = evolution_data[i-1][1]
            change = f"+{label-prev_label}号"
        
        print(f"│ {stage:<11} │   {label:2d}号   │   {legend:2d}号   │   {tick:2d}号   │  {change:<6} │")
    
    print("└─────────────┴──────────┴──────────┴──────────┴──────────┘")
    
    print("\n22号字体的特点:")
    print("• 📏 标签字体: 22号 - 超大字体，远距离可见")
    print("• 📏 图例字体: 16号 - 大字体，清晰易读")
    print("• 📏 刻度字体: 15号 - 中大字体，数值清晰")
    print("• 🎨 字体类型: SimSun (宋体) - 传统中文字体")
    print("• 💪 字体粗细: 标签粗体，其他正常")
    
    print("\n适用场景对比:")
    print("┌─────────────┬─────────────────────────────────────┐")
    print("│  字体大小   │              适用场景               │")
    print("├─────────────┼─────────────────────────────────────┤")
    print("│ 12-14号     │ 个人电脑屏幕，日常分析              │")
    print("│ 18号        │ 小型会议室，笔记本演示              │")
    print("│ 22号        │ 大型会议室，投影仪，海报展示        │")
    print("└─────────────┴─────────────────────────────────────┘")

def show_technical_specs():
    """显示技术规格"""
    print("\n" + "=" * 60)
    print("22号字体技术规格")
    print("=" * 60)
    
    print("字体设置:")
    print("• 标签字体: 22号，粗体，SimSun")
    print("• 图例字体: 16号，正常，SimSun")
    print("• 刻度字体: 15号，正常，SimSun")
    print("• 基础字体: 10号，正常，SimSun")
    
    print("\n图表规格:")
    print("• 基本图表: 16×10英寸 (从14×9英寸增大)")
    print("• 峰值分析: 18×12英寸 (从16×10英寸增大)")
    print("• 分辨率: 400 DPI (保持不变)")
    print("• 线条粗细: 3.5像素 (从3.0像素增加)")
    
    print("\n间距调整:")
    print("• 标签间距: 15-18像素 (从12-15像素增加)")
    print("• 图表边距: 3.0像素 (从2.0像素增加)")
    print("• 网格线宽: 1.2像素 (从1.0像素增加)")
    
    print("\n质量保证:")
    print("• 图例透明度: 98% (几乎不透明)")
    print("• 网格透明度: 40% (适中)")
    print("• 标记大小: 160像素 (峰值分析)")
    print("• 边框线宽: 1.5像素 (图例边框)")

if __name__ == "__main__":
    print("=" * 60)
    print("22号字体大小设置验证")
    print("=" * 60)
    
    # 运行验证
    test1 = verify_font_size_22_settings()
    test2 = create_quick_test_22()
    
    print("\n" + "=" * 60)
    if test1 and test2:
        print("✓ 22号字体大小设置验证成功！")
        print("所有绘图现在都使用22号标签字体")
    else:
        print("✗ 22号字体大小设置验证失败")
    print("=" * 60)
    
    # 显示详细信息
    show_font_size_evolution()
    show_technical_specs()
    
    print("\n" + "=" * 60)
    print("验证完成！测试图表保存在 test_results/ 目录下")
    print("=" * 60)
