#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证18号字体大小设置
"""

import numpy as np
import os

def verify_font_size_settings():
    """验证字体大小设置"""
    print("验证18号字体大小设置...")
    
    try:
        from utils.plot_utils import get_chinese_font_prop
        
        # 测试不同字体大小设置
        label_font = get_chinese_font_prop(size=18, weight='bold')
        legend_font = get_chinese_font_prop(size=14, weight='normal')
        tick_font = get_chinese_font_prop(size=13)
        
        print(f"✓ 标签字体设置: {label_font.get_size()}号, 粗细: {label_font.get_weight()}")
        print(f"✓ 图例字体设置: {legend_font.get_size()}号, 粗细: {legend_font.get_weight()}")
        print(f"✓ 刻度字体设置: {tick_font.get_size()}号")
        
        # 验证字体大小是否正确
        if label_font.get_size() == 18:
            print("✓ 标签字体大小正确：18号")
        else:
            print(f"✗ 标签字体大小错误：{label_font.get_size()}号，应该是18号")
            return False
            
        if legend_font.get_size() == 14:
            print("✓ 图例字体大小正确：14号")
        else:
            print(f"✗ 图例字体大小错误：{legend_font.get_size()}号，应该是14号")
            return False
            
        if tick_font.get_size() == 13:
            print("✓ 刻度字体大小正确：13号")
        else:
            print(f"✗ 刻度字体大小错误：{tick_font.get_size()}号，应该是13号")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 字体大小验证失败: {e}")
        return False

def create_quick_test():
    """创建快速测试图表"""
    print("创建快速测试图表...")
    
    try:
        from utils.plot_utils import create_high_clarity_plot
        
        # 创建简单测试数据
        np.random.seed(42)
        predicted_values = np.random.rand(25) * 40 + 30
        actual_values = np.random.rand(25) * 40 + 30
        
        # 创建测试目录
        if not os.path.exists('test_results'):
            os.makedirs('test_results')
        
        # 测试高清晰度绘图
        create_high_clarity_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            station_id=4,
            title=None,
            save_path='test_results/quick_font_18_test.png',
            show_plot=False,
            figsize=(12, 8),
            dpi=400
        )
        
        print("✓ 快速测试图表创建成功")
        return True
        
    except Exception as e:
        print(f"✗ 快速测试图表创建失败: {e}")
        return False

def show_font_size_summary():
    """显示字体大小设置总结"""
    print("\n" + "=" * 50)
    print("字体大小设置总结")
    print("=" * 50)
    
    print("新的字体大小设置:")
    print("┌─────────────┬──────────┬──────────┬──────────┐")
    print("│    元素     │  新大小  │  旧大小  │   变化   │")
    print("├─────────────┼──────────┼──────────┼──────────┤")
    print("│ 坐标轴标签  │   18号   │   14号   │  +4号    │")
    print("│ 图例文字    │   14号   │   12号   │  +2号    │")
    print("│ 刻度标签    │   13号   │   11号   │  +2号    │")
    print("│ 基础字体    │   10号   │   10号   │  不变    │")
    print("└─────────────┴──────────┴──────────┴──────────┘")
    
    print("\n字体特性:")
    print("• 字体类型: SimSun (宋体)")
    print("• 标签粗细: 粗体 (bold)")
    print("• 图例粗细: 正常 (normal)")
    print("• 刻度粗细: 正常 (normal)")
    
    print("\n适用场景:")
    print("• 📊 大屏幕显示和演示")
    print("• 📄 高质量打印输出")
    print("• 👥 会议和报告展示")
    print("• 📱 高分辨率图表")

if __name__ == "__main__":
    print("=" * 60)
    print("18号字体大小设置验证")
    print("=" * 60)
    
    # 运行验证
    test1 = verify_font_size_settings()
    test2 = create_quick_test()
    
    print("\n" + "=" * 60)
    if test1 and test2:
        print("✓ 18号字体大小设置验证成功！")
        print("所有绘图现在都使用18号标签字体")
    else:
        print("✗ 18号字体大小设置验证失败")
    print("=" * 60)
    
    # 显示总结
    show_font_size_summary()
