import numpy as np
import matplotlib.pyplot as plt
import os
from scipy.signal import find_peaks

# 添加中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def extract_station_peaks():
    """
    提取5个指定车站(4, 18, 30, 60, 94)的5个预测峰值及对应的真实值
    """
    # 检查结果文件是否存在
    if not os.path.exists('result/X_original.txt') or not os.path.exists('result/Y_prediction.txt'):
        print("错误: 预测结果文件不存在，请先运行main_predict.py生成预测结果")
        return
    
    # 加载预测值和真实值
    try:
        x_data = np.loadtxt('result/X_original.txt')
        y_data = np.loadtxt('result/Y_prediction.txt')
    except Exception as e:
        print(f"加载数据出错: {e}")
        return
    
    # 创建结果目录
    if not os.path.exists('result/peaks_data'):
        os.makedirs('result/peaks_data')
    
    # 创建汇总文件
    summary_file = 'result/peaks_data/peaks_summary.txt'
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("五个车站峰值数据汇总\n\n")
        f.write("站点ID\t峰值序号\t时间步\t预测值\t真实值\n")
    
    station_ids = [4, 18, 30, 60, 94]  # 指定的5个站点ID
    
    # 为每个车站提取峰值
    for i, station_id in enumerate(station_ids):
        station_pred = x_data[i]
        station_true = y_data[i]
        
        # 找出高峰值
        peaks, _ = find_peaks(station_pred, distance=5)
        peak_values_pred = station_pred[peaks]
        peak_values_true = station_true[peaks]
        
        # 获取前5个最大高峰值
        if len(peaks) >= 5:
            top_indices = np.argsort(peak_values_pred)[-5:]
            top_peaks_pred = peak_values_pred[top_indices]
            top_peaks_true = peak_values_true[top_indices]
            top_peaks_time = peaks[top_indices]
        else:
            # 如果峰值不足5个，则直接排序取最大的几个值
            top_indices = np.argsort(station_pred)[-5:]
            top_peaks_pred = station_pred[top_indices]
            top_peaks_true = station_true[top_indices]
            top_peaks_time = top_indices
        
        # 打印结果
        print(f"\n========== 车站{station_id}的5个峰值 ==========")
        print("峰值序号\t时间步\t预测值\t真实值")
        for j in range(len(top_peaks_pred)):
            print(f"{j+1}\t{top_peaks_time[j]}\t{top_peaks_pred[j]:.1f}\t{top_peaks_true[j]:.1f}")
        
        # 计算峰值平均值
        peak_avg_pred = np.mean(top_peaks_pred)
        peak_avg_true = np.mean(top_peaks_true)
        print(f"\n峰值平均值 - 预测: {peak_avg_pred:.2f}, 真实: {peak_avg_true:.2f}")
        
        # 导入改进的绘图工具
        from utils.plot_utils import create_peak_analysis_plot

        # 使用改进的峰值分析绘图函数（无标题）
        create_peak_analysis_plot(
            predicted_values=station_pred,
            actual_values=station_true,
            peaks_pred_time=top_peaks_time,
            peaks_pred_val=top_peaks_pred,
            peaks_true_time=top_peaks_time,
            peaks_true_val=top_peaks_true,
            station_id=station_id,
            title=None,  # 隐藏标题
            save_path=f'result/peaks_data/station_{station_id}_peaks.png',
            show_plot=False,
            figsize=(12, 6)
        )
        
        # 保存结果到文本文件
        with open(f'result/peaks_data/station_{station_id}_peaks.txt', 'w', encoding='utf-8') as f:
            f.write(f"车站{station_id}的5个峰值\n\n")
            f.write("峰值序号\t时间步\t预测值\t真实值\n")
            for j in range(len(top_peaks_pred)):
                f.write(f"{j+1}\t{top_peaks_time[j]}\t{top_peaks_pred[j]:.1f}\t{top_peaks_true[j]:.1f}\n")
            f.write(f"\n峰值平均值 - 预测: {peak_avg_pred:.2f}, 真实: {peak_avg_true:.2f}\n")
        
        # 添加到汇总文件
        with open(summary_file, 'a', encoding='utf-8') as f:
            for j in range(len(top_peaks_pred)):
                f.write(f"{station_id}\t{j+1}\t{top_peaks_time[j]}\t{top_peaks_pred[j]:.1f}\t{top_peaks_true[j]:.1f}\n")
            f.write(f"站点{station_id}峰值平均值:\t\t\t{peak_avg_pred:.2f}\t{peak_avg_true:.2f}\n\n")
    
    # 创建一个包含所有站点峰值的对比图
    plt.figure(figsize=(15, 10))
    
    for i, station_id in enumerate(station_ids):
        plt.subplot(3, 2, i+1)
        station_pred = x_data[i]
        station_true = y_data[i]
        
        # 找出高峰值
        peaks, _ = find_peaks(station_pred, distance=5)
        peak_values_pred = station_pred[peaks]
        
        # 获取前5个最大高峰值
        if len(peaks) >= 5:
            top_indices = np.argsort(peak_values_pred)[-5:]
            top_peaks_time = peaks[top_indices]
        else:
            top_indices = np.argsort(station_pred)[-5:]
            top_peaks_time = top_indices
        
        plt.plot(station_pred, color="r", label="预测值")
        plt.plot(station_true, color="b", label="真实值")
        plt.scatter(top_peaks_time, station_pred[top_peaks_time], color="darkred", s=80, marker="^")
        plt.scatter(top_peaks_time, station_true[top_peaks_time], color="darkblue", s=80, marker="^")

        # plt.title(f"车站{station_id}")  # 隐藏子图标题
        plt.xlabel("时间步")
        plt.ylabel("客流量")
        if i == 0:
            plt.legend(loc='best')
        plt.grid(True, linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig('result/peaks_data/all_stations_peaks_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\n所有车站的峰值分析已完成，结果保存在 {os.path.abspath('result/peaks_data/')} 目录下")
    print(f"汇总信息保存在 {os.path.abspath(summary_file)}")

if __name__ == "__main__":
    extract_station_peaks()