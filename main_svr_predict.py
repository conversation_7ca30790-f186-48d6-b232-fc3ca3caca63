import numpy as np
import os
import time
import matplotlib.pyplot as plt
# 添加中文字体支持
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
from model.SVR_model import SVRModel
from utils.metrics import Metrics, Metrics_1d
from data.get_dataloader import get_inflow_dataloader, get_outflow_dataloader
import torch

# 设置参数
time_interval = 15
time_lag = 10
tg_in_one_day = 72
forecast_day_number = 5
pre_len = 1
batch_size = 32
station_num = 276
model_type = 'svr'
TIMESTAMP = str(time.strftime("%Y_%m_%d_%H_%M_%S"))
save_dir = './save_model/' + model_type + '_' + TIMESTAMP
if not os.path.exists(save_dir):
    os.makedirs(save_dir)

# 加载数据
print("加载数据...")
inflow_data_loader_train, inflow_data_loader_val, inflow_data_loader_test, max_inflow, min_inflow = \
    get_inflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day,
                          forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=batch_size)
outflow_data_loader_train, outflow_data_loader_val, outflow_data_loader_test, max_outflow, min_outflow = \
    get_outflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day,
                           forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=batch_size)

# 将数据加载器中的数据转换为NumPy数组
def dataloader_to_numpy(dataloader):
    X_list = []
    y_list = []
    for batch in dataloader:
        # 检查批次是否包含2个或3个元素
        if len(batch) == 2:
            X, y = batch
        else:  # 如果有3个元素，取前两个
            X, y, _ = batch
        X_list.append(X.numpy())
        y_list.append(y.numpy())
    return np.vstack(X_list), np.vstack(y_list)

print("转换训练数据...")
X_train, y_train = dataloader_to_numpy(inflow_data_loader_train)
print("转换验证数据...")
X_val, y_val = dataloader_to_numpy(inflow_data_loader_val)
print("转换测试数据...")
X_test, y_test = dataloader_to_numpy(inflow_data_loader_test)

print(f"训练数据形状: X={X_train.shape}, y={y_train.shape}")
print(f"验证数据形状: X={X_val.shape}, y={y_val.shape}")
print(f"测试数据形状: X={X_test.shape}, y={y_test.shape}")

# 初始化SVR模型
print("初始化SVR模型...")
svr_model = SVRModel(station_num, time_lag, pre_len, save_dir=save_dir)

# 选择特定的5个站点进行训练和评估
selected_stations = [4, 18, 30, 60, 94]  # 指定5个特定站点
print(f"选择 {len(selected_stations)} 个站点进行训练")
print(f"选择的站点: {selected_stations}")

# 训练模型
print("开始训练SVR模型...")
svr_model.train(X_train, y_train, selected_stations=selected_stations)

# 测试
print("开始测试SVR模型...")
predictions = svr_model.predict(X_test, selected_stations=selected_stations)

# 转换回原始尺度
predictions = predictions * max_inflow
y_test_original = y_test * max_inflow

# 取整并处理负值
predictions = np.array(predictions).astype(np.int64)
predictions[predictions < 0] = 0
y_test_original = np.array(y_test_original).astype(np.int64)
y_test_original[y_test_original < 0] = 0

# 选择特定站点进行可视化
x = [[] for _ in range(len(selected_stations))]
y = [[] for _ in range(len(selected_stations))]

# 填充可视化数据
for i, station_idx in enumerate(selected_stations):
    for j in range(predictions.shape[0]):
        x[i].append(predictions[j, station_idx, 0])
        y[i].append(y_test_original[j, station_idx, 0])

# 计算各站点评估指标
print("\n========== 各站点预测结果评估指标 ==========")
for i, station_idx in enumerate(selected_stations):
    RMSE, R2, MAE, WMAPE = Metrics_1d(y[i], x[i]).evaluate_performance()
    print(f"站点 {station_idx} - RMSE: {RMSE:.4f}, R2: {R2:.4f}, MAE: {MAE:.4f}, WMAPE: {WMAPE:.4f}")
    
    # 保存评估指标
    station_metrics = [RMSE, MAE, WMAPE]
    
    # 创建结果目录
    if not os.path.exists('result/svr'):
        os.makedirs('result/svr')
    
    np.savetxt(f'result/svr/svr_station_{station_idx}_metrics.txt', station_metrics)
    
    # 导入改进的绘图工具
    from utils.plot_utils import create_improved_plot

    # 使用改进的绘图函数创建图表（无标题）
    create_improved_plot(
        predicted_values=x[i],
        actual_values=y[i],
        station_id=station_idx,
        title=None,  # 隐藏标题
        save_path=f'result/svr/station_{station_idx}_prediction.png',
        show_plot=False
    )

# 计算所有选定站点的整体评估指标
all_predictions = []
all_ground_truth = []

# 使用所有选定的站点数据计算整体指标
for i, station_idx in enumerate(selected_stations):
    all_predictions.extend(x[i])
    all_ground_truth.extend(y[i])

RMSE, R2, MAE, WMAPE = Metrics_1d(all_ground_truth, all_predictions).evaluate_performance()

print("\n========== SVR模型预测结果整体评估指标 (5个站点) ==========")
print(f"RMSE (均方根误差): {RMSE:.4f}")
print(f"R2 (决定系数): {R2:.4f}")
print(f"MAE (平均绝对误差): {MAE:.4f}")
print(f"WMAPE (加权平均绝对百分比误差): {WMAPE:.4f}")
print("=====================================\n")

# 保存整体评估指标
ALL = [RMSE, MAE, WMAPE]
np.savetxt(f'result/svr/svr_ALL.txt', ALL)

print("SVR模型训练和评估完成！")