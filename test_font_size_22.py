#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试22号标签字体大小设置
"""

import numpy as np
import matplotlib.pyplot as plt
import os
from utils.plot_utils import (configure_chinese_font, get_chinese_font_prop, 
                             create_high_clarity_plot, create_high_clarity_peak_analysis_plot)

def test_font_size_22():
    """测试22号标签字体"""
    print("测试22号标签字体设置...")
    
    # 配置字体
    configure_chinese_font()
    
    # 创建测试数据
    np.random.seed(42)
    time_steps = 50
    predicted_values = 55 + 25 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
    actual_values = 55 + 25 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 创建测试目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    try:
        # 创建手动设置字体大小的图表
        fig, ax = plt.subplots(figsize=(16, 10))  # 增大图表尺寸以适应大字体
        
        # 绘制数据
        time_axis = np.arange(len(predicted_values))
        ax.plot(time_axis, predicted_values, color="red", linewidth=3.5, label="预测值", alpha=0.9)
        ax.plot(time_axis, actual_values, color="blue", linewidth=3.5, label="实际值", alpha=0.9)
        
        # 设置坐标轴刻度线在内侧
        ax.tick_params(axis='both', which='major', direction='in', length=10, width=2, labelsize=15)
        
        # 设置坐标轴范围
        ax.set_xlim(0, len(predicted_values) - 1)
        
        # 设置网格
        ax.grid(True, linestyle='--', alpha=0.4, linewidth=1.2)
        
        # 使用新的字体大小设置
        label_font = get_chinese_font_prop(size=22, weight='bold')      # 标签22号
        legend_font = get_chinese_font_prop(size=16, weight='normal')   # 图例16号
        tick_font = get_chinese_font_prop(size=15)                     # 刻度15号
        
        # 设置标签
        ax.set_xlabel("时间步", fontproperties=label_font, labelpad=15)
        ax.set_ylabel("客流量", fontproperties=label_font, labelpad=15)
        
        # 设置图例
        legend = ax.legend(loc='upper right', frameon=True, fancybox=True, 
                          shadow=True, framealpha=0.98, prop=legend_font,
                          edgecolor='black', facecolor='white')
        legend.get_frame().set_linewidth(1.5)
        
        # 设置刻度标签字体
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontproperties(tick_font)
        
        # 添加字体大小说明
        ax.text(0.02, 0.98, "标签字体: 22号\n图例字体: 16号\n刻度字体: 15号", 
                transform=ax.transAxes, 
                fontproperties=get_chinese_font_prop(size=12), 
                verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
        
        plt.tight_layout(pad=3.0)
        plt.savefig('test_results/font_size_22_test.png', dpi=400, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print("✓ 22号标签字体测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 22号标签字体测试失败: {e}")
        return False

def test_high_clarity_with_22():
    """测试高清晰度版本的22号字体"""
    print("测试高清晰度版本22号字体...")
    
    # 创建测试数据
    np.random.seed(123)
    time_steps = 70
    predicted_values = 65 + 20 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    actual_values = 65 + 20 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    try:
        # 测试高清晰度绘图
        create_high_clarity_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            station_id=4,
            title=None,
            save_path='test_results/high_clarity_font_22_test.png',
            show_plot=False,
            figsize=(16, 10),  # 增大图表尺寸
            dpi=400
        )
        
        print("✓ 高清晰度版本22号字体测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 高清晰度版本22号字体测试失败: {e}")
        return False

def test_peak_analysis_with_22():
    """测试峰值分析的22号字体"""
    print("测试峰值分析22号字体...")
    
    # 创建测试数据
    np.random.seed(456)
    time_steps = 60
    predicted_values = 50 + 30 * np.sin(np.linspace(0, 4*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
    actual_values = 50 + 30 * np.sin(np.linspace(0, 4*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 生成峰值和低谷数据
    peaks_indices = [12, 27, 42, 57]
    valleys_indices = [5, 20, 35, 50]
    
    peaks_pred_time = peaks_indices
    peaks_pred_val = [predicted_values[i] for i in peaks_indices]
    peaks_true_time = peaks_indices
    peaks_true_val = [actual_values[i] for i in peaks_indices]
    
    valleys_pred_time = valleys_indices
    valleys_pred_val = [predicted_values[i] for i in valleys_indices]
    valleys_true_time = valleys_indices
    valleys_true_val = [actual_values[i] for i in valleys_indices]
    
    try:
        # 测试峰值分析绘图
        create_high_clarity_peak_analysis_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            peaks_pred_time=peaks_pred_time,
            peaks_pred_val=peaks_pred_val,
            peaks_true_time=peaks_true_time,
            peaks_true_val=peaks_true_val,
            valleys_pred_time=valleys_pred_time,
            valleys_pred_val=valleys_pred_val,
            valleys_true_time=valleys_true_time,
            valleys_true_val=valleys_true_val,
            station_id=4,
            title=None,
            save_path='test_results/peak_analysis_font_22_test.png',
            show_plot=False,
            figsize=(18, 12),  # 进一步增大图表尺寸
            dpi=400
        )
        
        print("✓ 峰值分析22号字体测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 峰值分析22号字体测试失败: {e}")
        return False

def create_font_size_progression():
    """创建字体大小演进对比图"""
    print("创建字体大小演进对比图...")
    
    # 测试数据
    x = np.arange(12)
    y = np.random.rand(12) * 35 + 25
    
    fig, axes = plt.subplots(1, 4, figsize=(20, 6))
    fig.suptitle('字体大小演进对比 (SimSun字体)', fontsize=18, fontweight='bold')
    
    font_configs = [
        (12, 10, 9, "原始设置"),
        (14, 12, 11, "第一次调整"),
        (18, 14, 13, "第二次调整"),
        (22, 16, 15, "当前设置")
    ]
    
    colors = ['blue', 'green', 'orange', 'red']
    
    for i, (label_size, legend_size, tick_size, title) in enumerate(font_configs):
        ax = axes[i]
        ax.plot(x, y, 'o-', linewidth=2.5, markersize=7, label="测试数据", color=colors[i])
        ax.tick_params(axis='both', which='major', direction='in', labelsize=tick_size)
        ax.grid(True, linestyle='--', alpha=0.3)
        
        try:
            label_font = get_chinese_font_prop(size=label_size, weight='bold')
            legend_font = get_chinese_font_prop(size=legend_size)
            tick_font = get_chinese_font_prop(size=tick_size)
            
            ax.set_xlabel("时间步", fontproperties=label_font)
            ax.set_ylabel("客流量", fontproperties=label_font)
            ax.set_title(f"{title}\n标签{label_size}号/图例{legend_size}号/刻度{tick_size}号", 
                        fontsize=11, fontweight='bold')
            ax.legend(prop=legend_font)
            
            # 设置刻度标签字体
            for label in ax.get_xticklabels() + ax.get_yticklabels():
                label.set_fontproperties(tick_font)
            
        except Exception as e:
            ax.set_xlabel("时间步", fontsize=label_size, fontweight='bold')
            ax.set_ylabel("客流量", fontsize=label_size, fontweight='bold')
            ax.set_title(f"{title} (设置失败)", fontsize=11)
            ax.legend(fontsize=legend_size)
    
    plt.tight_layout()
    plt.savefig('test_results/font_size_progression.png', dpi=300, 
                bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✓ 字体大小演进对比图创建完成")

if __name__ == "__main__":
    import os
    
    # 创建测试结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    print("=" * 60)
    print("22号标签字体大小测试")
    print("=" * 60)
    
    # 运行测试
    test1 = test_font_size_22()
    test2 = test_high_clarity_with_22()
    test3 = test_peak_analysis_with_22()
    create_font_size_progression()
    
    print("\n" + "=" * 60)
    if test1 and test2 and test3:
        print("✓ 所有22号字体测试通过！")
        print("字体大小已成功调整到22号")
    else:
        print("✗ 部分测试失败，需要进一步检查")
    print("=" * 60)
    
    print("\n最新的字体大小设置:")
    print("1. 📏 标签字体: 22号 (从18号增大)")
    print("2. 📏 图例字体: 16号 (从14号增大)")
    print("3. 📏 刻度字体: 14-15号 (从12-13号增大)")
    print("4. 📏 基础字体: 10号 (保持不变)")
    print("5. 🎨 字体类型: SimSun (宋体)")
    print("6. 📊 分辨率: 400 DPI (保持不变)")
    print("7. 📐 图表尺寸: 16×10英寸 / 18×12英寸 (适应大字体)")
    print("8. 测试结果保存在 test_results/ 目录下")
    
    print("\n适用场景:")
    print("• 🖥️ 大屏幕演示和会议室显示")
    print("• 📊 高分辨率打印和海报制作")
    print("• 👥 远距离观看的图表展示")
    print("• 📱 超高清显示设备")
