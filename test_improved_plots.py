#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的绘图功能
"""

import numpy as np
import matplotlib.pyplot as plt
from utils.plot_utils import create_improved_plot, create_peak_analysis_plot, create_multiple_stations_plot

def test_improved_plotting():
    """测试改进的绘图功能"""
    
    print("开始测试改进的绘图功能...")
    
    # 生成测试数据
    np.random.seed(42)
    time_steps = 100
    
    # 生成模拟的客流量数据
    base_flow = 50 + 30 * np.sin(np.linspace(0, 4*np.pi, time_steps))
    noise_pred = np.random.normal(0, 5, time_steps)
    noise_true = np.random.normal(0, 3, time_steps)
    
    predicted_values = base_flow + noise_pred
    actual_values = base_flow + noise_true
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    print("1. 测试基本的改进绘图功能...")
    
    # 测试基本绘图
    create_improved_plot(
        predicted_values=predicted_values,
        actual_values=actual_values,
        station_id=4,
        title="测试站点客流量预测结果",
        save_path='test_results/test_basic_plot.png',
        show_plot=False
    )
    print("   ✓ 基本绘图测试完成")
    
    print("2. 测试峰值分析绘图功能...")
    
    # 生成峰值和低谷数据
    peaks_indices = [20, 45, 70, 95]
    valleys_indices = [10, 35, 60, 85]
    
    peaks_pred_time = peaks_indices
    peaks_pred_val = [predicted_values[i] for i in peaks_indices]
    peaks_true_time = peaks_indices
    peaks_true_val = [actual_values[i] for i in peaks_indices]
    
    valleys_pred_time = valleys_indices
    valleys_pred_val = [predicted_values[i] for i in valleys_indices]
    valleys_true_time = valleys_indices
    valleys_true_val = [actual_values[i] for i in valleys_indices]
    
    # 测试峰值分析绘图
    create_peak_analysis_plot(
        predicted_values=predicted_values,
        actual_values=actual_values,
        peaks_pred_time=peaks_pred_time,
        peaks_pred_val=peaks_pred_val,
        peaks_true_time=peaks_true_time,
        peaks_true_val=peaks_true_val,
        valleys_pred_time=valleys_pred_time,
        valleys_pred_val=valleys_pred_val,
        valleys_true_time=valleys_true_time,
        valleys_true_val=valleys_true_val,
        station_id=4,
        title="测试站点峰值分析",
        save_path='test_results/test_peak_analysis_plot.png',
        show_plot=False
    )
    print("   ✓ 峰值分析绘图测试完成")
    
    print("3. 测试多站点绘图功能...")
    
    # 生成多个站点的测试数据
    station_ids = [4, 18, 30, 60, 94]
    x_data = []
    y_data = []
    
    for i in range(5):
        # 为每个站点生成不同的数据
        base_flow_station = 40 + 20 * np.sin(np.linspace(0, 3*np.pi + i*0.5, time_steps))
        noise_pred_station = np.random.normal(0, 4, time_steps)
        noise_true_station = np.random.normal(0, 2, time_steps)
        
        pred_station = np.maximum(base_flow_station + noise_pred_station, 0)
        true_station = np.maximum(base_flow_station + noise_true_station, 0)
        
        x_data.append(pred_station)
        y_data.append(true_station)
    
    # 测试多站点绘图
    create_multiple_stations_plot(
        x_data=x_data,
        y_data=y_data,
        station_ids=station_ids,
        save_dir='test_results/multiple_stations',
        model_name="test_model"
    )
    print("   ✓ 多站点绘图测试完成")
    
    print("4. 测试图表特性...")
    
    # 创建一个显示图表来验证特性
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    time_steps_array = np.arange(len(predicted_values))
    
    # 绘制数据
    ax.plot(time_steps_array, predicted_values, color="red", linewidth=2, 
            label="预测值", alpha=0.8)
    ax.plot(time_steps_array, actual_values, color="blue", linewidth=2, 
            label="实际值", alpha=0.8)
    
    # 验证特性1: 坐标轴刻度线在内侧
    ax.tick_params(axis='both', which='major', direction='in', length=6, width=1)
    ax.tick_params(axis='both', which='minor', direction='in', length=3, width=0.5)
    
    # 验证特性2: 显示起始值 (x轴从0开始)
    ax.set_xlim(0, len(predicted_values) - 1)
    
    # 验证特性3: 图例清晰且不压图线
    legend = ax.legend(loc='upper right', frameon=True, fancybox=True, 
                      shadow=True, framealpha=0.9, fontsize=11)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_edgecolor('gray')
    legend.get_frame().set_linewidth(0.5)
    
    # 设置网格和标签
    ax.grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
    ax.set_xlabel("时间步", fontsize=12, fontweight='bold')
    ax.set_ylabel("客流量", fontsize=12, fontweight='bold')
    ax.set_title("改进绘图特性验证", fontsize=14, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig('test_results/test_features_verification.png', dpi=300, 
                bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()
    
    print("   ✓ 图表特性验证完成")
    print("     - 刻度线已设置为内侧")
    print("     - 坐标轴显示起始值(从0开始)")
    print("     - 图例清晰且不压图线")
    
    print("\n所有测试完成！")
    print("测试结果保存在 test_results/ 目录下")
    print("\n改进的绘图功能包括:")
    print("1. ✓ 坐标轴刻度线放在内侧")
    print("2. ✓ 坐标图显示起始值(x轴从0开始)")
    print("3. ✓ 图例清晰且不压图线")
    print("4. ✓ 更好的网格和标签样式")
    print("5. ✓ 高分辨率输出(300 DPI)")
    print("6. ✓ 支持峰值分析可视化")
    print("7. ✓ 支持多站点批量绘图")

if __name__ == "__main__":
    import os
    
    # 创建测试结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    if not os.path.exists('test_results/multiple_stations'):
        os.makedirs('test_results/multiple_stations')
    
    # 运行测试
    test_improved_plotting()
