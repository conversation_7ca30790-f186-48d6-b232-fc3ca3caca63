#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的extract_station_peaks.py紧凑型绘图效果
"""

import numpy as np
import os

def simulate_extract_peaks_compact():
    """模拟extract_station_peaks.py的紧凑型绘图调用"""
    print("模拟extract_station_peaks.py紧凑型绘图...")
    
    try:
        from utils.compact_plot_utils import create_compact_peak_analysis_plot
        
        # 创建模拟数据，类似extract_station_peaks.py中的数据
        np.random.seed(42)
        time_steps = 100
        
        # 模拟站点预测和真实数据
        station_pred = 60 + 30 * np.sin(np.linspace(0, 4*np.pi, time_steps)) + np.random.normal(0, 5, time_steps)
        station_true = 60 + 30 * np.sin(np.linspace(0, 4*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
        
        # 确保值为正数
        station_pred = np.maximum(station_pred, 0)
        station_true = np.maximum(station_true, 0)
        
        # 模拟峰值检测结果
        top_peaks_time = [20, 45, 70, 95]
        top_peaks_pred = [station_pred[i] for i in top_peaks_time]
        top_peaks_true = [station_true[i] for i in top_peaks_time]
        
        # 模拟低谷检测结果
        bottom_valleys_time = [10, 35, 60, 85]
        bottom_valleys_pred = [station_pred[i] for i in bottom_valleys_time]
        bottom_valleys_true = [station_true[i] for i in bottom_valleys_time]
        
        real_station_id = 4
        
        # 创建测试目录
        if not os.path.exists('test_results'):
            os.makedirs('test_results')
        
        # 模拟extract_station_peaks.py中的紧凑型绘图调用
        create_compact_peak_analysis_plot(
            predicted_values=station_pred,
            actual_values=station_true,
            peaks_pred_time=top_peaks_time,
            peaks_pred_val=top_peaks_pred,
            peaks_true_time=top_peaks_time,
            peaks_true_val=top_peaks_true,
            valleys_pred_time=bottom_valleys_time,
            valleys_pred_val=bottom_valleys_pred,
            valleys_true_time=bottom_valleys_time,
            valleys_true_val=bottom_valleys_true,
            station_id=real_station_id,
            title=None,  # 隐藏标题
            save_path=f'test_results/compact_station_{real_station_id}_peaks_valleys_test.png',
            show_plot=False,
            width_cm=6,       # 6cm宽度
            height_cm=4,      # 4cm高度
            dpi=300           # 300 DPI分辨率
        )
        
        print("✓ extract_station_peaks.py紧凑型绘图模拟成功")
        return True
        
    except Exception as e:
        print(f"✗ extract_station_peaks.py紧凑型绘图模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_stations_compact():
    """测试多个站点的紧凑型绘图"""
    print("测试多个站点的紧凑型绘图...")
    
    try:
        from utils.compact_plot_utils import create_compact_peak_analysis_plot
        
        # 测试多个站点
        station_ids = [4, 18, 30]
        
        success_count = 0
        
        for station_id in station_ids:
            # 为每个站点创建不同的数据
            np.random.seed(42 + station_id)
            time_steps = 80
            
            station_pred = 50 + 25 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
            station_true = 50 + 25 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
            
            station_pred = np.maximum(station_pred, 0)
            station_true = np.maximum(station_true, 0)
            
            # 生成峰值和低谷
            top_peaks_time = [15, 40, 65]
            top_peaks_pred = [station_pred[i] for i in top_peaks_time]
            top_peaks_true = [station_true[i] for i in top_peaks_time]
            
            bottom_valleys_time = [8, 30, 55]
            bottom_valleys_pred = [station_pred[i] for i in bottom_valleys_time]
            bottom_valleys_true = [station_true[i] for i in bottom_valleys_time]
            
            try:
                create_compact_peak_analysis_plot(
                    predicted_values=station_pred,
                    actual_values=station_true,
                    peaks_pred_time=top_peaks_time,
                    peaks_pred_val=top_peaks_pred,
                    peaks_true_time=top_peaks_time,
                    peaks_true_val=top_peaks_true,
                    valleys_pred_time=bottom_valleys_time,
                    valleys_pred_val=bottom_valleys_pred,
                    valleys_true_time=bottom_valleys_time,
                    valleys_true_val=bottom_valleys_true,
                    station_id=station_id,
                    title=None,
                    save_path=f'test_results/compact_multi_station_{station_id}_test.png',
                    show_plot=False,
                    width_cm=6,
                    height_cm=4,
                    dpi=300
                )
                
                print(f"  ✓ 站点{station_id}紧凑型绘图成功")
                success_count += 1
                
            except Exception as e:
                print(f"  ✗ 站点{station_id}紧凑型绘图失败: {e}")
        
        return success_count == len(station_ids)
        
    except Exception as e:
        print(f"✗ 多站点紧凑型绘图测试失败: {e}")
        return False

def check_file_properties():
    """检查生成文件的属性"""
    print("检查生成文件的属性...")
    
    import os
    from PIL import Image
    
    test_files = [
        'test_results/compact_station_4_peaks_valleys_test.png',
        'test_results/compact_multi_station_4_test.png',
        'test_results/compact_multi_station_18_test.png',
        'test_results/compact_multi_station_30_test.png'
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                # 获取文件大小
                file_size = os.path.getsize(file_path)
                file_size_kb = file_size / 1024
                
                # 获取图片尺寸和DPI
                with Image.open(file_path) as img:
                    width_px, height_px = img.size
                    dpi = img.info.get('dpi', (300, 300))
                    
                    # 计算实际尺寸（厘米）
                    width_cm = (width_px / dpi[0]) * 2.54
                    height_cm = (height_px / dpi[1]) * 2.54
                
                print(f"  📄 {os.path.basename(file_path)}:")
                print(f"    - 文件大小: {file_size_kb:.1f} KB")
                print(f"    - 像素尺寸: {width_px}×{height_px}")
                print(f"    - 实际尺寸: {width_cm:.1f}×{height_cm:.1f} cm")
                print(f"    - 分辨率: {dpi[0]}×{dpi[1]} DPI")
                
                # 检查是否符合要求
                if width_cm <= 6.1:  # 允许小误差
                    print(f"    ✓ 宽度符合要求 (≤6cm)")
                else:
                    print(f"    ✗ 宽度超出要求 (>{width_cm:.1f}cm)")
                
                if dpi[0] >= 290:  # 允许小误差
                    print(f"    ✓ 分辨率符合要求 (≥300 DPI)")
                else:
                    print(f"    ✗ 分辨率不符合要求 (<300 DPI)")
                
            except Exception as e:
                print(f"  ✗ 无法分析文件 {file_path}: {e}")
        else:
            print(f"  ✗ 文件不存在: {file_path}")

def show_compact_summary():
    """显示紧凑型绘图总结"""
    print("\n" + "=" * 60)
    print("紧凑型绘图工具总结")
    print("=" * 60)
    
    print("设计目标:")
    print("• 📏 图片宽度: ≤6cm")
    print("• 📝 标签字体: 6号宋体")
    print("• 📊 分辨率: 300 DPI")
    print("• 💾 文件大小: 50-100KB")
    
    print("\n技术规格:")
    print("┌─────────────┬──────────┬──────────┬──────────┐")
    print("│    元素     │   字体   │   大小   │   样式   │")
    print("├─────────────┼──────────┼──────────┼──────────┤")
    print("│ 坐标轴标签  │  宋体    │   6号    │   粗体   │")
    print("│ 图例文字    │  宋体    │   5号    │   正常   │")
    print("│ 刻度标签    │  宋体    │   4号    │   正常   │")
    print("│ 基础字体    │  宋体    │   6号    │   正常   │")
    print("└─────────────┴──────────┴──────────┴──────────┘")
    
    print("\n图表规格:")
    print("• 默认尺寸: 6×4cm")
    print("• 线条粗细: 1.0像素")
    print("• 标记大小: 15像素")
    print("• 网格线宽: 0.3像素")
    print("• 标签间距: 2像素")
    
    print("\n适用场景:")
    print("• 📄 学术论文插图")
    print("• 📊 技术报告小图")
    print("• 📱 网页展示图表")
    print("• 📧 邮件附件图表")
    print("• 📋 多图表页面布局")
    
    print("\n文件输出:")
    print("• 文件前缀: compact_")
    print("• 图片格式: PNG")
    print("• 颜色模式: RGB")
    print("• 压缩质量: 高质量")

if __name__ == "__main__":
    import os
    
    # 创建测试结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    print("=" * 60)
    print("extract_station_peaks.py 紧凑型绘图测试")
    print("=" * 60)
    
    # 运行测试
    test1 = simulate_extract_peaks_compact()
    test2 = test_multiple_stations_compact()
    
    print("\n" + "=" * 60)
    if test1 and test2:
        print("✓ 所有紧凑型绘图测试通过！")
        print("extract_station_peaks.py 修改成功")
    else:
        print("✗ 部分测试失败，需要进一步检查")
    print("=" * 60)
    
    # 检查文件属性
    print("\n文件属性检查:")
    check_file_properties()
    
    # 显示总结
    show_compact_summary()
    
    print("\n" + "=" * 60)
    print("测试完成！可以运行以下命令测试实际效果:")
    print("python extract_station_peaks.py --station 0")
    print("=" * 60)
