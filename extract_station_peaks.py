import numpy as np
import matplotlib.pyplot as plt
import os
from scipy.signal import find_peaks
import argparse

# 添加中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def extract_station_peaks_valleys(station_id=0):
    """
    提取指定车站的高峰和低谷预测值与真实值
    
    参数:
        station_id: 车站ID，默认为0
    """
    # 检查结果文件是否存在
    if not os.path.exists('result/X_original.txt') or not os.path.exists('result/Y_prediction.txt'):
        print("错误: 预测结果文件不存在，请先运行main_predict.py生成预测结果")
        return
    
    # 加载预测值和真实值
    try:
        x_data = np.loadtxt('result/X_original.txt')
        y_data = np.loadtxt('result/Y_prediction.txt')
    except Exception as e:
        print(f"加载数据出错: {e}")
        return
    
    # 检查车站ID是否有效
    if station_id < 0 or station_id >= 5:  # 当前代码中只保存了5个站点的数据
        print(f"错误: 无效的车站ID {station_id}，有效范围为0-4")
        station_id = 0 # 默认使用第一个站点
        print(f"使用默认车站ID: {station_id}")
    
    # 获取对应车站的预测值和真实值
    station_pred = x_data[station_id]
    station_true = y_data[station_id]
    
    # 找出高峰值
    peaks, _ = find_peaks(station_pred, distance=5)
    peak_values_pred = station_pred[peaks]
    peak_values_true = station_true[peaks]
    
    # 找出低谷值 (通过对负值找峰值)
    valleys, _ = find_peaks(-station_pred, distance=5)
    valley_values_pred = station_pred[valleys]
    valley_values_true = station_true[valleys]
    
    # 获取前5个最大高峰值
    if len(peaks) >= 5:
        top_indices = np.argsort(peak_values_pred)[-5:]
        top_peaks_pred = peak_values_pred[top_indices]
        top_peaks_true = peak_values_true[top_indices]
        top_peaks_time = peaks[top_indices]
    else:
        # 如果峰值不足5个，则直接排序取最大的几个值
        top_indices = np.argsort(station_pred)[-5:]
        top_peaks_pred = station_pred[top_indices]
        top_peaks_true = station_true[top_indices]
        top_peaks_time = top_indices
    
    # 获取前5个最低低谷值
    if len(valleys) >= 5:
        bottom_indices = np.argsort(valley_values_pred)[:5]
        bottom_valleys_pred = valley_values_pred[bottom_indices]
        bottom_valleys_true = valley_values_true[bottom_indices]
        bottom_valleys_time = valleys[bottom_indices]
    else:
        # 如果低谷不足5个，则直接排序取最小的几个值
        bottom_indices = np.argsort(station_pred)[:5]
        bottom_valleys_pred = station_pred[bottom_indices]
        bottom_valleys_true = station_true[bottom_indices]
        bottom_valleys_time = bottom_indices
    
    # 打印结果
    station_ids = [4, 18, 30, 60, 94]  # main_predict.py中使用的站点ID
    real_station_id = station_ids[station_id]
    
    print(f"\n========== 车站{real_station_id}的高峰和低谷分析 ==========")
    print("\n前5个高峰值:")
    print("时间步\t预测值\t真实值")
    for i in range(len(top_peaks_pred)):
        print(f"{top_peaks_time[i]}\t{top_peaks_pred[i]:.1f}\t{top_peaks_true[i]:.1f}")
    
    print("\n前5个低谷值:")
    print("时间步\t预测值\t真实值")
    for i in range(len(bottom_valleys_pred)):
        print(f"{bottom_valleys_time[i]}\t{bottom_valleys_pred[i]:.1f}\t{bottom_valleys_true[i]:.1f}")
    
    # 计算高峰和低谷的平均值
    peak_avg_pred = np.mean(top_peaks_pred)
    peak_avg_true = np.mean(top_peaks_true)
    valley_avg_pred = np.mean(bottom_valleys_pred)
    valley_avg_true = np.mean(bottom_valleys_true)
    
    print(f"\n高峰平均值 - 预测: {peak_avg_pred:.2f}, 真实: {peak_avg_true:.2f}")
    print(f"低谷平均值 - 预测: {valley_avg_pred:.2f}, 真实: {valley_avg_true:.2f}")
    
    # 导入改进的绘图工具
    from utils.plot_utils import create_peak_analysis_plot

    # 使用改进的峰值分析绘图函数（无标题）
    create_peak_analysis_plot(
        predicted_values=station_pred,
        actual_values=station_true,
        peaks_pred_time=top_peaks_time,
        peaks_pred_val=top_peaks_pred,
        peaks_true_time=top_peaks_time,
        peaks_true_val=top_peaks_true,
        valleys_pred_time=bottom_valleys_time,
        valleys_pred_val=bottom_valleys_pred,
        valleys_true_time=bottom_valleys_time,
        valleys_true_val=bottom_valleys_true,
        station_id=real_station_id,
        title=None,  # 隐藏标题
        save_path=None,
        show_plot=False,
        figsize=(14, 7)
    )
    
    # 创建结果目录
    if not os.path.exists('result/peaks_analysis'):
        os.makedirs('result/peaks_analysis')
    
    # 保存图表
    plt.savefig(f'result/peaks_analysis/station_{real_station_id}_peaks_valleys.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存结果到文本文件
    with open(f'result/peaks_analysis/station_{real_station_id}_analysis.txt', 'w', encoding='utf-8') as f:
        f.write(f"车站{real_station_id}的高峰和低谷分析\n\n")
        f.write("前5个高峰值:\n")
        f.write("时间步\t预测值\t真实值\n")
        for i in range(len(top_peaks_pred)):
            f.write(f"{top_peaks_time[i]}\t{top_peaks_pred[i]:.1f}\t{top_peaks_true[i]:.1f}\n")
        
        f.write("\n前5个低谷值:\n")
        f.write("时间步\t预测值\t真实值\n")
        for i in range(len(bottom_valleys_pred)):
            f.write(f"{bottom_valleys_time[i]}\t{bottom_valleys_pred[i]:.1f}\t{bottom_valleys_true[i]:.1f}\n")
        
        f.write(f"\n高峰平均值 - 预测: {peak_avg_pred:.2f}, 真实: {peak_avg_true:.2f}\n")
        f.write(f"低谷平均值 - 预测: {valley_avg_pred:.2f}, 真实: {valley_avg_true:.2f}\n")
    
    return {
        'station_id': real_station_id,
        'peaks_pred': top_peaks_pred,
        'peaks_true': top_peaks_true,
        'valleys_pred': bottom_valleys_pred,
        'valleys_true': bottom_valleys_true,
        'peak_avg_pred': peak_avg_pred,
        'peak_avg_true': peak_avg_true,
        'valley_avg_pred': valley_avg_pred,
        'valley_avg_true': valley_avg_true
    }

def extract_all_stations_peaks_valleys():
    """
    提取所有五个车站的高峰和低谷预测值与真实值
    """
    # 检查结果文件是否存在
    if not os.path.exists('result/X_original.txt') or not os.path.exists('result/Y_prediction.txt'):
        print("错误: 预测结果文件不存在，请先运行main_predict.py生成预测结果")
        return
    
    # 加载预测值和真实值
    try:
        x_data = np.loadtxt('result/X_original.txt')
        y_data = np.loadtxt('result/Y_prediction.txt')
    except Exception as e:
        print(f"加载数据出错: {e}")
        return
    
    # 创建结果目录
    if not os.path.exists('result/peaks_analysis'):
        os.makedirs('result/peaks_analysis')
    
    # 创建汇总文件
    summary_file = 'result/peaks_analysis/all_stations_summary.txt'
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("所有车站高峰和低谷分析汇总\n\n")
    
    station_ids = [4, 18, 30, 60, 94]  # main_predict.py中使用的站点ID
    all_results = []
    
    # 为每个车站提取峰值和谷值
    for i in range(5):
        station_pred = x_data[i]
        station_true = y_data[i]
        real_station_id = station_ids[i]
        
        # 找出高峰值
        peaks, _ = find_peaks(station_pred, distance=5)
        peak_values_pred = station_pred[peaks]
        peak_values_true = station_true[peaks]
        
        # 找出低谷值 (通过对负值找峰值)
        valleys, _ = find_peaks(-station_pred, distance=5)
        valley_values_pred = station_pred[valleys]
        valley_values_true = station_true[valleys]
        
        # 获取前5个最大高峰值
        if len(peaks) >= 5:
            top_indices = np.argsort(peak_values_pred)[-5:]
            top_peaks_pred = peak_values_pred[top_indices]
            top_peaks_true = peak_values_true[top_indices]
            top_peaks_time = peaks[top_indices]
        else:
            # 如果峰值不足5个，则直接排序取最大的几个值
            top_indices = np.argsort(station_pred)[-5:]
            top_peaks_pred = station_pred[top_indices]
            top_peaks_true = station_true[top_indices]
            top_peaks_time = top_indices
        
        # 获取前5个最低低谷值
        if len(valleys) >= 5:
            bottom_indices = np.argsort(valley_values_pred)[:5]
            bottom_valleys_pred = valley_values_pred[bottom_indices]
            bottom_valleys_true = valley_values_true[bottom_indices]
            bottom_valleys_time = valleys[bottom_indices]
        else:
            # 如果低谷不足5个，则直接排序取最小的几个值
            bottom_indices = np.argsort(station_pred)[:5]
            bottom_valleys_pred = station_pred[bottom_indices]
            bottom_valleys_true = station_true[bottom_indices]
            bottom_valleys_time = bottom_indices
        
        # 计算高峰和低谷的平均值
        peak_avg_pred = np.mean(top_peaks_pred)
        peak_avg_true = np.mean(top_peaks_true)
        valley_avg_pred = np.mean(bottom_valleys_pred)
        valley_avg_true = np.mean(bottom_valleys_true)
        
        # 打印结果
        print(f"\n========== 车站{real_station_id}的高峰和低谷分析 ==========")
        print("\n前5个高峰值:")
        print("时间步\t预测值\t真实值")
        for j in range(len(top_peaks_pred)):
            print(f"{top_peaks_time[j]}\t{top_peaks_pred[j]:.1f}\t{top_peaks_true[j]:.1f}")
        
        print("\n前5个低谷值:")
        print("时间步\t预测值\t真实值")
        for j in range(len(bottom_valleys_pred)):
            print(f"{bottom_valleys_time[j]}\t{bottom_valleys_pred[j]:.1f}\t{bottom_valleys_true[j]:.1f}")
        
        print(f"\n高峰平均值 - 预测: {peak_avg_pred:.2f}, 真实: {peak_avg_true:.2f}")
        print(f"低谷平均值 - 预测: {valley_avg_pred:.2f}, 真实: {valley_avg_true:.2f}")

        # 导入改进的绘图工具
        from utils.plot_utils import create_peak_analysis_plot
        
        # 使用改进的峰值分析绘图函数（无标题）
        create_peak_analysis_plot(
            predicted_values=station_pred,
            actual_values=station_true,
            peaks_pred_time=top_peaks_time,
            peaks_pred_val=top_peaks_pred,
            peaks_true_time=top_peaks_time,
            peaks_true_val=top_peaks_true,
            valleys_pred_time=bottom_valleys_time,
            valleys_pred_val=bottom_valleys_pred,
            valleys_true_time=bottom_valleys_time,
            valleys_true_val=bottom_valleys_true,
            station_id=real_station_id,
            title=None,  # 隐藏标题
            save_path=f'result/peaks_analysis/station_{real_station_id}_peaks_valleys.png',
            show_plot=False,
            figsize=(14, 7)
        )
        
        # 保存结果到文本文件
        with open(f'result/peaks_analysis/station_{real_station_id}_analysis.txt', 'w', encoding='utf-8') as f:
            f.write(f"车站{real_station_id}的高峰和低谷分析\n\n")
            f.write("前5个高峰值:\n")
            f.write("时间步\t预测值\t真实值\n")
            for j in range(len(top_peaks_pred)):
                f.write(f"{top_peaks_time[j]}\t{top_peaks_pred[j]:.1f}\t{top_peaks_true[j]:.1f}\n")
            
            f.write("\n前5个低谷值:\n")
            f.write("时间步\t预测值\t真实值\n")
            for j in range(len(bottom_valleys_pred)):
                f.write(f"{bottom_valleys_time[j]}\t{bottom_valleys_pred[j]:.1f}\t{bottom_valleys_true[j]:.1f}\n")
            
            f.write(f"\n高峰平均值 - 预测: {peak_avg_pred:.2f}, 真实: {peak_avg_true:.2f}\n")
            f.write(f"低谷平均值 - 预测: {valley_avg_pred:.2f}, 真实: {valley_avg_true:.2f}\n")
        
        # 添加到汇总文件
        with open(summary_file, 'a', encoding='utf-8') as f:
            f.write(f"车站{real_station_id}:\n")
            f.write(f"高峰平均值 - 预测: {peak_avg_pred:.2f}, 真实: {peak_avg_true:.2f}\n")
            f.write(f"低谷平均值 - 预测: {valley_avg_pred:.2f}, 真实: {valley_avg_true:.2f}\n\n")
        
        # 保存结果
        result = {
            'station_id': real_station_id,
            'peaks_pred': top_peaks_pred,
            'peaks_true': top_peaks_true,
            'valleys_pred': bottom_valleys_pred,
            'valleys_true': bottom_valleys_true,
            'peak_avg_pred': peak_avg_pred,
            'peak_avg_true': peak_avg_true,
            'valley_avg_pred': valley_avg_pred,
            'valley_avg_true': valley_avg_true
        }
        all_results.append(result)
    
    print(f"\n所有车站的高峰和低谷分析已完成，结果保存在 {os.path.abspath('result/peaks_analysis/')} 目录下")
    print(f"汇总信息保存在 {os.path.abspath(summary_file)}")
    
    return all_results

if __name__ == "__main__":
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='提取指定车站的高峰和低谷预测值与真实值')
    parser.add_argument('--station', type=int, default=-1, help='车站索引 (0-4，对应站点ID 4,18,30,60,94)，-1表示处理所有车站')
    args = parser.parse_args()
    
    if args.station == -1:
        # 提取所有车站的高峰和低谷
        extract_all_stations_peaks_valleys()
    else:
        # 提取指定车站的高峰和低谷
        extract_station_peaks_valleys(args.station)