import numpy as np
import os, time, torch
from torch import nn
from torch.utils.tensorboard import SummaryWriter
from utils.utils import GetLaplacian
from model.main_model import Model
from utils.earlystopping import EarlyStopping
from data.get_dataloader import get_inflow_dataloader, get_outflow_dataloader
from skopt import gp_minimize
from skopt.space import Real, Integer
from skopt.utils import use_named_args
import skopt

# 设置设备
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 固定参数
time_interval = 15
time_lag = 10
tg_in_one_day = 72
forecast_day_number = 5
pre_len = 1
station_num = 276
model_type = 'bayesian_opt'

# 定义参数空间
dim_learning_rate = Real(low=1e-4, high=1e-2, prior='log-uniform', name='learning_rate')
dim_kernel_size = Integer(low=2, high=5, name='kernel_size')

# 合并参数空间
dimensions = [dim_learning_rate, dim_kernel_size]

# 加载数据
inflow_data_loader_train, inflow_data_loader_val, inflow_data_loader_test, max_inflow, min_inflow = \
    get_inflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day, 
                         forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=32)
outflow_data_loader_train, outflow_data_loader_val, outflow_data_loader_test, max_outflow, min_outflow = \
    get_outflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day, 
                          forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=32)

# 获取邻接矩阵
adjacency = np.loadtxt('./data/adjacency.csv', delimiter=",")
adjacency = torch.tensor(GetLaplacian(adjacency).get_normalized_adj(station_num)).type(torch.float32).to(device)

# 用于初始化卷积层的参数
def weights_init(m):
    classname = m.__class__.__name__
    if classname.find('Conv2d') != -1:
        nn.init.xavier_normal_(m.weight.data)
        nn.init.constant_(m.bias.data, 0.0)
    if classname.find('ConvTranspose2d') != -1:
        nn.init.xavier_normal_(m.weight.data)
        nn.init.constant_(m.bias.data, 0.0)

# 定义目标函数
@use_named_args(dimensions=dimensions)
def objective(learning_rate, kernel_size):
    try:
        print(f"尝试参数: 学习率={learning_rate}, 卷积核大小={kernel_size}")
        
        # 创建保存目录
        TIMESTAMP = str(time.strftime("%Y_%m_%d_%H_%M_%S"))
        save_dir = f'./save_model/{model_type}_lr_{learning_rate:.6f}_kernel_{kernel_size}_{TIMESTAMP}'
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        # 创建模型
        model = Model(time_lag=time_lag, pre_len=pre_len, station_num=station_num, 
                     device=device, kernel_size=kernel_size)
        model.apply(weights_init)
        model = model.to(device)
        
        # 优化器
        optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
        mse = torch.nn.MSELoss().to(device)
        
        # 早停
        early_stopping = EarlyStopping(patience=20, verbose=True)
        
        # 训练参数
        max_epochs = 100  # 减少训练轮数以加快优化过程
        best_val_loss = float('inf')
        
        # 训练循环
        for epoch in range(max_epochs):
            # 训练阶段
            model.train()
            train_loss = 0
            for inflow_tr, outflow_tr in zip(enumerate(inflow_data_loader_train), enumerate(outflow_data_loader_train)):
                i_batch, (train_inflow_X, train_inflow_Y) = inflow_tr
                i_batch, (train_outflow_X, train_outflow_Y) = outflow_tr
                
                train_inflow_X = train_inflow_X.type(torch.float32).to(device)
                train_inflow_Y = train_inflow_Y.type(torch.float32).to(device)
                train_outflow_X = train_outflow_X.type(torch.float32).to(device)
                train_outflow_Y = train_outflow_Y.type(torch.float32).to(device)
                
                target = model(train_inflow_X, train_outflow_X, adjacency)
                loss = mse(input=train_inflow_Y, target=target)
                
                train_loss += loss.item()
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
            
            # 验证阶段
            model.eval()
            val_loss = 0
            with torch.no_grad():
                for inflow_val, outflow_val in zip(enumerate(inflow_data_loader_val), enumerate(outflow_data_loader_val)):
                    i_batch, (val_inflow_X, val_inflow_Y) = inflow_val
                    i_batch, (val_outflow_X, val_outflow_Y) = outflow_val
                    
                    val_inflow_X = val_inflow_X.type(torch.float32).to(device)
                    val_inflow_Y = val_inflow_Y.type(torch.float32).to(device)
                    val_outflow_X = val_outflow_X.type(torch.float32).to(device)
                    val_outflow_Y = val_outflow_Y.type(torch.float32).to(device)
                    
                    target = model(val_inflow_X, val_outflow_X, adjacency)
                    loss = mse(input=val_inflow_Y, target=target)
                    val_loss += loss.item()
            
            # 计算平均损失
            avg_train_loss = train_loss / len(inflow_data_loader_train)
            avg_val_loss = val_loss / len(inflow_data_loader_val)
            
            print(f'Epoch: {epoch}, Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}')
            
            # 更新最佳验证损失
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
            
            # 早停检查
            early_stopping(avg_val_loss, model.state_dict(), model, epoch, save_dir)
            if early_stopping.early_stop:
                print("早停触发！")
                break
        
        # 返回最佳验证损失作为优化目标
        return best_val_loss
    except Exception as e:
        print(f"在评估参数时发生错误: {e}")
        # 返回一个非常大的值，表示这组参数无效
        return 1e10

# 执行贝叶斯优化
result = gp_minimize(func=objective,
                    dimensions=dimensions,
                    acq_func='EI',  # 使用期望改进作为采集函数
                    n_calls=15,     # 总共尝试的参数组合数
                    n_random_starts=5,  # 随机初始化的次数
                    verbose=True)

# 打印最佳参数
print("最佳参数:")
print(f"学习率: {result.x[0]}")
print(f"卷积核大小: {result.x[1]}")

# 保存优化结果
skopt.dump(result, 'bayesian_optimization_result.pkl', store_objective=False)

# 将最佳参数写入文件
with open('best_params.txt', 'w') as f:
    f.write(f"学习率: {result.x[0]}\n")
    f.write(f"卷积核大小: {result.x[1]}\n")
    f.write(f"最佳验证损失: {result.fun}\n")

print("优化完成！")