#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SimSun字体设置
"""

import numpy as np
import matplotlib.pyplot as plt
import os
from utils.plot_utils import (configure_chinese_font, get_chinese_font_prop, 
                             create_high_clarity_plot, create_high_clarity_peak_analysis_plot)

def test_simsun_font_basic():
    """测试SimSun字体基本设置"""
    print("测试SimSun字体基本设置...")
    
    # 配置字体
    configure_chinese_font()
    
    # 创建测试数据
    np.random.seed(42)
    time_steps = 50
    predicted_values = 50 + 20 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    actual_values = 50 + 20 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 创建测试目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    try:
        # 创建基本图表测试SimSun字体
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 绘制数据
        time_axis = np.arange(len(predicted_values))
        ax.plot(time_axis, predicted_values, color="red", linewidth=2.5, label="预测值", alpha=0.9)
        ax.plot(time_axis, actual_values, color="blue", linewidth=2.5, label="实际值", alpha=0.9)
        
        # 设置坐标轴刻度线在内侧
        ax.tick_params(axis='both', which='major', direction='in', length=8, width=1.5, labelsize=11)
        
        # 设置坐标轴范围
        ax.set_xlim(0, len(predicted_values) - 1)
        
        # 设置网格
        ax.grid(True, linestyle='--', alpha=0.4, linewidth=0.8)
        
        # 使用SimSun字体设置
        label_font = get_chinese_font_prop(size=14, weight='bold')
        legend_font = get_chinese_font_prop(size=12, weight='normal')
        tick_font = get_chinese_font_prop(size=11)
        
        # 设置标签
        ax.set_xlabel("时间步", fontproperties=label_font, labelpad=10)
        ax.set_ylabel("客流量", fontproperties=label_font, labelpad=10)
        
        # 设置图例
        legend = ax.legend(loc='upper right', frameon=True, fancybox=True, 
                          shadow=True, framealpha=0.98, prop=legend_font,
                          edgecolor='black', facecolor='white')
        legend.get_frame().set_linewidth(1.2)
        
        # 设置刻度标签字体
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontproperties(tick_font)
        
        plt.tight_layout(pad=2.0)
        plt.savefig('test_results/simsun_basic_test.png', dpi=400, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print("✓ SimSun字体基本测试成功")
        return True
        
    except Exception as e:
        print(f"✗ SimSun字体基本测试失败: {e}")
        return False

def test_simsun_high_clarity_plot():
    """测试SimSun字体高清晰度绘图"""
    print("测试SimSun字体高清晰度绘图...")
    
    # 创建测试数据
    np.random.seed(123)
    time_steps = 80
    predicted_values = 60 + 25 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    actual_values = 60 + 25 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    try:
        # 测试高清晰度绘图
        create_high_clarity_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            station_id=4,
            title=None,
            save_path='test_results/simsun_high_clarity_test.png',
            show_plot=False,
            figsize=(14, 9),
            dpi=400
        )
        
        print("✓ SimSun字体高清晰度绘图测试成功")
        return True
        
    except Exception as e:
        print(f"✗ SimSun字体高清晰度绘图测试失败: {e}")
        return False

def test_simsun_peak_analysis():
    """测试SimSun字体峰值分析绘图"""
    print("测试SimSun字体峰值分析绘图...")
    
    # 创建测试数据
    np.random.seed(456)
    time_steps = 60
    predicted_values = 40 + 20 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
    actual_values = 40 + 20 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 生成峰值和低谷数据
    peaks_indices = [15, 35, 55]
    valleys_indices = [5, 25, 45]
    
    peaks_pred_time = peaks_indices
    peaks_pred_val = [predicted_values[i] for i in peaks_indices]
    peaks_true_time = peaks_indices
    peaks_true_val = [actual_values[i] for i in peaks_indices]
    
    valleys_pred_time = valleys_indices
    valleys_pred_val = [predicted_values[i] for i in valleys_indices]
    valleys_true_time = valleys_indices
    valleys_true_val = [actual_values[i] for i in valleys_indices]
    
    try:
        # 测试峰值分析绘图
        create_high_clarity_peak_analysis_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            peaks_pred_time=peaks_pred_time,
            peaks_pred_val=peaks_pred_val,
            peaks_true_time=peaks_true_time,
            peaks_true_val=peaks_true_val,
            valleys_pred_time=valleys_pred_time,
            valleys_pred_val=valleys_pred_val,
            valleys_true_time=valleys_true_time,
            valleys_true_val=valleys_true_val,
            station_id=4,
            title=None,
            save_path='test_results/simsun_peak_analysis_test.png',
            show_plot=False,
            figsize=(16, 10),
            dpi=400
        )
        
        print("✓ SimSun字体峰值分析绘图测试成功")
        return True
        
    except Exception as e:
        print(f"✗ SimSun字体峰值分析绘图测试失败: {e}")
        return False

def create_font_comparison():
    """创建字体对比图"""
    print("创建字体对比图...")
    
    # 测试数据
    x = np.arange(20)
    y = np.random.rand(20) * 50 + 30
    
    fig, axes = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle('SimSun vs Microsoft YaHei 字体对比', fontsize=16, fontweight='bold')
    
    # SimSun字体
    ax1 = axes[0]
    ax1.plot(x, y, 'o-', linewidth=2, markersize=6, label="测试数据", color='blue')
    ax1.tick_params(axis='both', which='major', direction='in', labelsize=11)
    ax1.grid(True, linestyle='--', alpha=0.3)
    
    try:
        simsun_font = fm.FontProperties(family=['SimSun'], size=12, weight='bold')
        simsun_legend = fm.FontProperties(family=['SimSun'], size=11)
        
        ax1.set_xlabel("时间步", fontproperties=simsun_font)
        ax1.set_ylabel("客流量数值", fontproperties=simsun_font)
        ax1.set_title("SimSun字体", fontsize=14, fontweight='bold')
        ax1.legend(prop=simsun_legend)
        
    except Exception as e:
        ax1.set_xlabel("时间步", fontsize=12, fontweight='bold')
        ax1.set_ylabel("客流量数值", fontsize=12, fontweight='bold')
        ax1.set_title("SimSun字体 (设置失败)", fontsize=14)
        ax1.legend(fontsize=11)
    
    # Microsoft YaHei字体
    ax2 = axes[1]
    ax2.plot(x, y, 'o-', linewidth=2, markersize=6, label="测试数据", color='red')
    ax2.tick_params(axis='both', which='major', direction='in', labelsize=11)
    ax2.grid(True, linestyle='--', alpha=0.3)
    
    try:
        yahei_font = fm.FontProperties(family=['Microsoft YaHei'], size=12, weight='bold')
        yahei_legend = fm.FontProperties(family=['Microsoft YaHei'], size=11)
        
        ax2.set_xlabel("时间步", fontproperties=yahei_font)
        ax2.set_ylabel("客流量数值", fontproperties=yahei_font)
        ax2.set_title("Microsoft YaHei字体", fontsize=14, fontweight='bold')
        ax2.legend(prop=yahei_legend)
        
    except Exception as e:
        ax2.set_xlabel("时间步", fontsize=12, fontweight='bold')
        ax2.set_ylabel("客流量数值", fontsize=12, fontweight='bold')
        ax2.set_title("Microsoft YaHei字体 (设置失败)", fontsize=14)
        ax2.legend(fontsize=11)
    
    plt.tight_layout()
    plt.savefig('test_results/font_comparison_simsun.png', dpi=300, 
                bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✓ 字体对比图创建完成")

if __name__ == "__main__":
    import os
    
    # 创建测试结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    print("=" * 60)
    print("SimSun字体设置测试")
    print("=" * 60)
    
    # 运行测试
    test1 = test_simsun_font_basic()
    test2 = test_simsun_high_clarity_plot()
    test3 = test_simsun_peak_analysis()
    create_font_comparison()
    
    print("\n" + "=" * 60)
    if test1 and test2 and test3:
        print("✓ 所有SimSun字体测试通过！")
        print("字体已成功改回SimSun")
    else:
        print("✗ 部分测试失败，需要进一步检查")
    print("=" * 60)
    
    print("\nSimSun字体设置说明:")
    print("1. 主要字体: SimSun (宋体)")
    print("2. 备用字体: SimHei (黑体), Microsoft YaHei (微软雅黑)")
    print("3. 保持了高清晰度特性")
    print("4. 保持了400 DPI分辨率")
    print("5. 测试结果保存在 test_results/ 目录下")
