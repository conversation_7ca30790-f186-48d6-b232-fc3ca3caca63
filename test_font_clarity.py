#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字体清晰度改进
"""

import numpy as np
import matplotlib.pyplot as plt
from utils.plot_utils import (configure_chinese_font, create_improved_plot, 
                             create_high_clarity_plot, get_chinese_font_prop)

def test_font_clarity_comparison():
    """测试字体清晰度对比"""
    print("测试字体清晰度改进...")
    
    # 创建测试数据
    np.random.seed(42)
    time_steps = 100
    predicted_values = 50 + 20 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
    actual_values = 50 + 20 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('中文字体清晰度对比测试', fontsize=16, fontweight='bold')
    
    # 配置字体
    configure_chinese_font()
    
    # 测试1: 小字体 + SimSun
    ax1 = axes[0, 0]
    ax1.plot(predicted_values, color="red", linewidth=2, label="预测值", alpha=0.8)
    ax1.plot(actual_values, color="blue", linewidth=2, label="实际值", alpha=0.8)
    ax1.tick_params(axis='both', which='major', direction='in')
    ax1.grid(True, linestyle='--', alpha=0.3)
    
    small_font = get_chinese_font_prop(size=8)
    ax1.set_xlabel("时间步", fontproperties=small_font)
    ax1.set_ylabel("客流量", fontproperties=small_font)
    ax1.set_title("小字体 (8号)", fontsize=12)
    ax1.legend(prop=small_font)
    
    # 测试2: 中等字体 + Microsoft YaHei
    ax2 = axes[0, 1]
    ax2.plot(predicted_values, color="red", linewidth=2, label="预测值", alpha=0.8)
    ax2.plot(actual_values, color="blue", linewidth=2, label="实际值", alpha=0.8)
    ax2.tick_params(axis='both', which='major', direction='in')
    ax2.grid(True, linestyle='--', alpha=0.3)
    
    medium_font = get_chinese_font_prop(size=11)
    ax2.set_xlabel("时间步", fontproperties=medium_font)
    ax2.set_ylabel("客流量", fontproperties=medium_font)
    ax2.set_title("中等字体 (11号)", fontsize=12)
    ax2.legend(prop=medium_font)
    
    # 测试3: 大字体 + 粗体
    ax3 = axes[1, 0]
    ax3.plot(predicted_values, color="red", linewidth=2.5, label="预测值", alpha=0.9)
    ax3.plot(actual_values, color="blue", linewidth=2.5, label="实际值", alpha=0.9)
    ax3.tick_params(axis='both', which='major', direction='in', labelsize=11)
    ax3.grid(True, linestyle='--', alpha=0.4, linewidth=0.8)
    
    large_font = get_chinese_font_prop(size=14, weight='bold')
    legend_font = get_chinese_font_prop(size=12)
    ax3.set_xlabel("时间步", fontproperties=large_font)
    ax3.set_ylabel("客流量", fontproperties=large_font)
    ax3.set_title("大字体 + 粗体 (14号)", fontsize=12)
    ax3.legend(prop=legend_font)
    
    # 测试4: 超大字体 + 优化样式
    ax4 = axes[1, 1]
    ax4.plot(predicted_values, color="red", linewidth=3, label="预测值", alpha=0.9)
    ax4.plot(actual_values, color="blue", linewidth=3, label="实际值", alpha=0.9)
    ax4.tick_params(axis='both', which='major', direction='in', length=8, width=1.5, labelsize=12)
    ax4.grid(True, linestyle='--', alpha=0.4, linewidth=1.0)
    
    xlarge_font = get_chinese_font_prop(size=16, weight='bold')
    xlegend_font = get_chinese_font_prop(size=14)
    ax4.set_xlabel("时间步", fontproperties=xlarge_font, labelpad=10)
    ax4.set_ylabel("客流量", fontproperties=xlarge_font, labelpad=10)
    ax4.set_title("超大字体 + 优化 (16号)", fontsize=12)
    legend = ax4.legend(prop=xlegend_font, framealpha=0.98, edgecolor='black')
    legend.get_frame().set_linewidth(1.2)
    
    plt.tight_layout()
    plt.savefig('test_results/font_clarity_comparison.png', dpi=300, 
                bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✓ 字体清晰度对比图创建完成")

def test_improved_vs_high_clarity():
    """测试改进版本与高清晰度版本的对比"""
    print("测试改进版本与高清晰度版本...")
    
    # 创建测试数据
    np.random.seed(123)
    time_steps = 80
    predicted_values = 60 + 25 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    actual_values = 60 + 25 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 测试改进版本
    create_improved_plot(
        predicted_values=predicted_values,
        actual_values=actual_values,
        station_id=4,
        title=None,
        save_path='test_results/improved_plot_clarity.png',
        show_plot=False
    )
    print("✓ 改进版本图表创建完成")
    
    # 测试高清晰度版本
    create_high_clarity_plot(
        predicted_values=predicted_values,
        actual_values=actual_values,
        station_id=4,
        title=None,
        save_path='test_results/high_clarity_plot.png',
        show_plot=False,
        figsize=(14, 9),
        dpi=400
    )
    print("✓ 高清晰度版本图表创建完成")

def test_different_fonts():
    """测试不同字体的清晰度"""
    print("测试不同字体清晰度...")
    
    # 创建简单测试数据
    x = np.arange(20)
    y = np.random.rand(20) * 50 + 30
    
    fonts_to_test = [
        ('Microsoft YaHei', '微软雅黑'),
        ('SimHei', '黑体'),
        ('SimSun', '宋体'),
        ('KaiTi', '楷体')
    ]
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('不同中文字体清晰度测试', fontsize=16, fontweight='bold')
    
    for i, (font_name, font_display) in enumerate(fonts_to_test):
        ax = axes[i//2, i%2]
        ax.plot(x, y, 'o-', linewidth=2, markersize=6, label=f"测试数据 ({font_display})")
        ax.tick_params(axis='both', which='major', direction='in', labelsize=11)
        ax.grid(True, linestyle='--', alpha=0.3)
        
        try:
            import matplotlib.font_manager as fm
            font_prop = fm.FontProperties(family=[font_name], size=12, weight='bold')
            legend_prop = fm.FontProperties(family=[font_name], size=11)
            
            ax.set_xlabel("时间步", fontproperties=font_prop)
            ax.set_ylabel("客流量数值", fontproperties=font_prop)
            ax.set_title(f"{font_display} ({font_name})", fontsize=14)
            ax.legend(prop=legend_prop)
            
        except Exception as e:
            ax.set_xlabel("时间步", fontsize=12, fontweight='bold')
            ax.set_ylabel("客流量数值", fontsize=12, fontweight='bold')
            ax.set_title(f"{font_display} (不可用)", fontsize=14)
            ax.legend(fontsize=11)
    
    plt.tight_layout()
    plt.savefig('test_results/different_fonts_clarity.png', dpi=300, 
                bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✓ 不同字体清晰度测试完成")

if __name__ == "__main__":
    import os
    
    # 创建测试结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    print("=" * 60)
    print("字体清晰度改进测试")
    print("=" * 60)
    
    # 运行测试
    test_font_clarity_comparison()
    test_improved_vs_high_clarity()
    test_different_fonts()
    
    print("\n" + "=" * 60)
    print("✓ 所有字体清晰度测试完成！")
    print("测试结果保存在 test_results/ 目录下")
    print("=" * 60)
    
    print("\n字体清晰度改进说明:")
    print("1. 字体优先级: 微软雅黑 > 黑体 > 宋体")
    print("2. 字体大小: 标签14号，图例12号，刻度11号")
    print("3. 线条粗细: 2.5-3.0像素")
    print("4. 图片分辨率: 400 DPI")
    print("5. 图例透明度: 98%，带黑色边框")
    print("6. 网格透明度: 40%，线宽0.8-1.0像素")
