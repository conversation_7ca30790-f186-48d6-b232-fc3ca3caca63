#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试18号标签字体大小设置
"""

import numpy as np
import matplotlib.pyplot as plt
import os
from utils.plot_utils import (configure_chinese_font, get_chinese_font_prop, 
                             create_high_clarity_plot, create_high_clarity_peak_analysis_plot)

def test_font_size_18():
    """测试18号标签字体"""
    print("测试18号标签字体设置...")
    
    # 配置字体
    configure_chinese_font()
    
    # 创建测试数据
    np.random.seed(42)
    time_steps = 60
    predicted_values = 50 + 25 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
    actual_values = 50 + 25 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 创建测试目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    try:
        # 创建手动设置字体大小的图表
        fig, ax = plt.subplots(figsize=(14, 9))
        
        # 绘制数据
        time_axis = np.arange(len(predicted_values))
        ax.plot(time_axis, predicted_values, color="red", linewidth=3, label="预测值", alpha=0.9)
        ax.plot(time_axis, actual_values, color="blue", linewidth=3, label="实际值", alpha=0.9)
        
        # 设置坐标轴刻度线在内侧
        ax.tick_params(axis='both', which='major', direction='in', length=8, width=1.5, labelsize=13)
        
        # 设置坐标轴范围
        ax.set_xlim(0, len(predicted_values) - 1)
        
        # 设置网格
        ax.grid(True, linestyle='--', alpha=0.4, linewidth=1.0)
        
        # 使用新的字体大小设置
        label_font = get_chinese_font_prop(size=18, weight='bold')      # 标签18号
        legend_font = get_chinese_font_prop(size=14, weight='normal')   # 图例14号
        tick_font = get_chinese_font_prop(size=13)                     # 刻度13号
        
        # 设置标签
        ax.set_xlabel("时间步", fontproperties=label_font, labelpad=12)
        ax.set_ylabel("客流量", fontproperties=label_font, labelpad=12)
        
        # 设置图例
        legend = ax.legend(loc='upper right', frameon=True, fancybox=True, 
                          shadow=True, framealpha=0.98, prop=legend_font,
                          edgecolor='black', facecolor='white')
        legend.get_frame().set_linewidth(1.2)
        
        # 设置刻度标签字体
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontproperties(tick_font)
        
        # 添加字体大小说明
        ax.text(0.02, 0.98, "标签字体: 18号\n图例字体: 14号\n刻度字体: 13号", 
                transform=ax.transAxes, 
                fontproperties=get_chinese_font_prop(size=11), 
                verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout(pad=2.0)
        plt.savefig('test_results/font_size_18_test.png', dpi=400, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print("✓ 18号标签字体测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 18号标签字体测试失败: {e}")
        return False

def test_high_clarity_with_18():
    """测试高清晰度版本的18号字体"""
    print("测试高清晰度版本18号字体...")
    
    # 创建测试数据
    np.random.seed(123)
    time_steps = 80
    predicted_values = 60 + 20 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    actual_values = 60 + 20 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    try:
        # 测试高清晰度绘图
        create_high_clarity_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            station_id=4,
            title=None,
            save_path='test_results/high_clarity_font_18_test.png',
            show_plot=False,
            figsize=(14, 9),
            dpi=400
        )
        
        print("✓ 高清晰度版本18号字体测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 高清晰度版本18号字体测试失败: {e}")
        return False

def test_peak_analysis_with_18():
    """测试峰值分析的18号字体"""
    print("测试峰值分析18号字体...")
    
    # 创建测试数据
    np.random.seed(456)
    time_steps = 70
    predicted_values = 45 + 25 * np.sin(np.linspace(0, 4*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
    actual_values = 45 + 25 * np.sin(np.linspace(0, 4*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 生成峰值和低谷数据
    peaks_indices = [15, 35, 55]
    valleys_indices = [5, 25, 45, 65]
    
    peaks_pred_time = peaks_indices
    peaks_pred_val = [predicted_values[i] for i in peaks_indices]
    peaks_true_time = peaks_indices
    peaks_true_val = [actual_values[i] for i in peaks_indices]
    
    valleys_pred_time = valleys_indices
    valleys_pred_val = [predicted_values[i] for i in valleys_indices]
    valleys_true_time = valleys_indices
    valleys_true_val = [actual_values[i] for i in valleys_indices]
    
    try:
        # 测试峰值分析绘图
        create_high_clarity_peak_analysis_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            peaks_pred_time=peaks_pred_time,
            peaks_pred_val=peaks_pred_val,
            peaks_true_time=peaks_true_time,
            peaks_true_val=peaks_true_val,
            valleys_pred_time=valleys_pred_time,
            valleys_pred_val=valleys_pred_val,
            valleys_true_time=valleys_true_time,
            valleys_true_val=valleys_true_val,
            station_id=4,
            title=None,
            save_path='test_results/peak_analysis_font_18_test.png',
            show_plot=False,
            figsize=(16, 10),
            dpi=400
        )
        
        print("✓ 峰值分析18号字体测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 峰值分析18号字体测试失败: {e}")
        return False

def create_font_size_comparison():
    """创建字体大小对比图"""
    print("创建字体大小对比图...")
    
    # 测试数据
    x = np.arange(15)
    y = np.random.rand(15) * 40 + 30
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('字体大小对比测试 (SimSun字体)', fontsize=16, fontweight='bold')
    
    font_configs = [
        (12, 10, 9, "小字体"),
        (15, 12, 11, "中字体"),
        (18, 14, 13, "大字体(新设置)")
    ]
    
    for i, (label_size, legend_size, tick_size, title) in enumerate(font_configs):
        ax = axes[i]
        ax.plot(x, y, 'o-', linewidth=2, markersize=6, label="测试数据", color=['blue', 'green', 'red'][i])
        ax.tick_params(axis='both', which='major', direction='in', labelsize=tick_size)
        ax.grid(True, linestyle='--', alpha=0.3)
        
        try:
            label_font = get_chinese_font_prop(size=label_size, weight='bold')
            legend_font = get_chinese_font_prop(size=legend_size)
            tick_font = get_chinese_font_prop(size=tick_size)
            
            ax.set_xlabel("时间步", fontproperties=label_font)
            ax.set_ylabel("客流量", fontproperties=label_font)
            ax.set_title(f"{title}\n标签{label_size}号/图例{legend_size}号/刻度{tick_size}号", 
                        fontsize=12, fontweight='bold')
            ax.legend(prop=legend_font)
            
            # 设置刻度标签字体
            for label in ax.get_xticklabels() + ax.get_yticklabels():
                label.set_fontproperties(tick_font)
            
        except Exception as e:
            ax.set_xlabel("时间步", fontsize=label_size, fontweight='bold')
            ax.set_ylabel("客流量", fontsize=label_size, fontweight='bold')
            ax.set_title(f"{title} (设置失败)", fontsize=12)
            ax.legend(fontsize=legend_size)
    
    plt.tight_layout()
    plt.savefig('test_results/font_size_comparison.png', dpi=300, 
                bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✓ 字体大小对比图创建完成")

if __name__ == "__main__":
    import os
    
    # 创建测试结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    print("=" * 60)
    print("18号标签字体大小测试")
    print("=" * 60)
    
    # 运行测试
    test1 = test_font_size_18()
    test2 = test_high_clarity_with_18()
    test3 = test_peak_analysis_with_18()
    create_font_size_comparison()
    
    print("\n" + "=" * 60)
    if test1 and test2 and test3:
        print("✓ 所有18号字体测试通过！")
        print("字体大小已成功调整")
    else:
        print("✗ 部分测试失败，需要进一步检查")
    print("=" * 60)
    
    print("\n新的字体大小设置:")
    print("1. 📏 标签字体: 18号 (从14号增大)")
    print("2. 📏 图例字体: 14号 (从12号增大)")
    print("3. 📏 刻度字体: 12-13号 (从11号增大)")
    print("4. 📏 基础字体: 10号 (保持不变)")
    print("5. 🎨 字体类型: SimSun (宋体)")
    print("6. 📊 分辨率: 400 DPI (保持不变)")
    print("7. 测试结果保存在 test_results/ 目录下")
