import numpy as np
import os, time, torch
from torch import nn
from torch.utils.tensorboard import SummaryWriter
from utils.utils import GetLaplacian
from model.main_model import Model
from utils.earlystopping import EarlyStopping
from data.get_dataloader import get_inflow_dataloader, get_outflow_dataloader
import skopt

# 设置设备
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 加载最佳参数
try:
    # 尝试从贝叶斯优化结果加载
    result = skopt.load('bayesian_optimization_result.pkl')
    best_lr = result.x[0]
    best_kernel_size = result.x[1]
    print(f"从优化结果加载参数: 学习率={best_lr}, 卷积核大小={best_kernel_size}")
except:
    # 如果加载失败，使用默认值
    best_lr = 0.001
    best_kernel_size = 3
    print(f"使用默认参数: 学习率={best_lr}, 卷积核大小={best_kernel_size}")

# 训练参数
epoch_num = 1000
time_interval = 15
time_lag = 10
tg_in_one_day = 72
forecast_day_number = 5
pre_len = 1
batch_size = 32
station_num = 276
model_type = 'best_params'
TIMESTAMP = str(time.strftime("%Y_%m_%d_%H_%M_%S"))
save_dir = f'./save_model/{model_type}_{TIMESTAMP}'
if not os.path.exists(save_dir):
    os.makedirs(save_dir)

# 初始化TensorBoard
log_dir = f'./logs/model_viz_{TIMESTAMP}'
writer = SummaryWriter(log_dir=log_dir)
print(f"TensorBoard日志保存在: {log_dir}")

# 加载数据
inflow_data_loader_train, inflow_data_loader_val, inflow_data_loader_test, max_inflow, min_inflow = \
    get_inflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day, 
                         forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=batch_size)
outflow_data_loader_train, outflow_data_loader_val, outflow_data_loader_test, max_outflow, min_outflow = \
    get_outflow_dataloader(time_interval=time_interval, time_lag=time_lag, tg_in_one_day=tg_in_one_day, 
                          forecast_day_number=forecast_day_number, pre_len=pre_len, batch_size=batch_size)

# 获取邻接矩阵
adjacency = np.loadtxt('./data/adjacency.csv', delimiter=",")
adjacency = torch.tensor(GetLaplacian(adjacency).get_normalized_adj(station_num)).type(torch.float32).to(device)

# 初始化时间
global_start_time = time.time()

# 用于初始化卷积层的参数
def weights_init(m):
    classname = m.__class__.__name__
    if classname.find('Conv2d') != -1:
        nn.init.xavier_normal_(m.weight.data)
        nn.init.constant_(m.bias.data, 0.0)
    if classname.find('ConvTranspose2d') != -1:
        nn.init.xavier_normal_(m.weight.data)
        nn.init.constant_(m.bias.data, 0.0)

# 创建模型
model = Model(time_lag=time_lag, pre_len=pre_len, station_num=station_num, 
             device=device, kernel_size=best_kernel_size)
print(model)
model.apply(weights_init)
model = model.to(device)

# 添加模型图到TensorBoard
# 获取一批样本数据用于可视化
sample_inflow_batch = next(iter(inflow_data_loader_train))[0].to(device).type(torch.float32)
sample_outflow_batch = next(iter(outflow_data_loader_train))[0].to(device).type(torch.float32)
adjacency = adjacency.type(torch.float32)

# 添加模型图到TensorBoard
try:
    writer.add_graph(model, (sample_inflow_batch, sample_outflow_batch, adjacency))
    print("模型结构已添加到TensorBoard")
except Exception as e:
    print(f"添加模型图时出错: {e}")
    print("继续训练过程...")

# 优化器
optimizer = torch.optim.Adam(model.parameters(), lr=best_lr)
mse = torch.nn.MSELoss().to(device)

# 训练循环
temp_time = time.time()
early_stopping = EarlyStopping(patience=100, verbose=True)

for epoch in range(0, epoch_num):
    # 训练阶段
    train_loss = 0
    model.train()
    for inflow_tr, outflow_tr in zip(enumerate(inflow_data_loader_train), enumerate(outflow_data_loader_train)):
        i_batch, (train_inflow_X, train_inflow_Y) = inflow_tr
        i_batch, (train_outflow_X, train_outflow_Y) = outflow_tr
        
        # 训练循环中的数据处理部分
        train_inflow_X = train_inflow_X.type(torch.float32).to(device)
        train_inflow_Y = train_inflow_Y.type(torch.float32).to(device)
        train_outflow_X = train_outflow_X.type(torch.float32).to(device)
        train_outflow_Y = train_outflow_Y.type(torch.float32).to(device)
        
        target = model(train_inflow_X, train_outflow_X, adjacency)
        loss = mse(input=train_inflow_Y, target=target)
        
        train_loss += loss.item()
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

    # 验证阶段
    with torch.no_grad():
        model.eval()
        val_loss = 0
        for inflow_val, outflow_val in zip(enumerate(inflow_data_loader_val), enumerate(outflow_data_loader_val)):
            i_batch, (val_inflow_X, val_inflow_Y) = inflow_val
            i_batch, (val_outflow_X, val_outflow_Y) = outflow_val
            
            val_inflow_X = val_inflow_X.type(torch.float32).to(device)
            val_inflow_Y = val_inflow_Y.type(torch.float32).to(device)
            val_outflow_X = val_outflow_X.type(torch.float32).to(device)
            val_outflow_Y = val_outflow_Y.type(torch.float32).to(device)
            
            target = model(val_inflow_X, val_outflow_X, adjacency)
            loss = mse(input=val_inflow_Y, target=target)
            val_loss += loss.item()

    # 计算平均损失
    avg_train_loss = train_loss / len(inflow_data_loader_train)
    avg_val_loss = val_loss / len(inflow_data_loader_val)
    
    # 记录到TensorBoard
    writer.add_scalar("loss_train", avg_train_loss, epoch)
    writer.add_scalar("loss_eval", avg_val_loss, epoch)
    print(f'Epoch: {epoch}, Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}')

    # 早停检查
    if epoch > 0:
        model_dict = model.state_dict()
        early_stopping(avg_val_loss, model_dict, model, epoch, save_dir)
        if early_stopping.early_stop:
            print("早停触发！")
            break
    
    # 每10个epoch打印一次训练时间
    if epoch % 10 == 0:
        print(f"10个epoch的训练时间: {round(time.time() - temp_time, 2)}秒")
        temp_time = time.time()

# 记录总训练时间
global_end_time = time.time() - global_start_time
print(f"总训练时间: {global_end_time}秒")

# 保存训练时间
Train_time_ALL = [global_end_time]
np.savetxt(f'result/lr_{best_lr}_kernel_{best_kernel_size}_batch_size_{batch_size}_Train_time_ALL.txt', Train_time_ALL)

print("训练完成！")