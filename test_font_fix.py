#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字体修复是否有效
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from utils.plot_utils import configure_chinese_font, get_chinese_font_prop, create_improved_plot

def test_font_fix():
    """测试字体修复"""
    print("测试字体修复...")
    
    # 配置字体
    configure_chinese_font()
    
    # 测试字体属性获取
    try:
        chinese_font = get_chinese_font_prop()
        print("✓ 字体属性获取成功")
    except Exception as e:
        print(f"✗ 字体属性获取失败: {e}")
        return False
    
    # 创建测试数据
    np.random.seed(42)
    time_steps = 50
    predicted_values = 50 + 20 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    actual_values = 50 + 20 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 测试基本绘图
    try:
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 绘制数据
        time_axis = np.arange(len(predicted_values))
        ax.plot(time_axis, predicted_values, color="red", linewidth=2, label="预测值", alpha=0.8)
        ax.plot(time_axis, actual_values, color="blue", linewidth=2, label="实际值", alpha=0.8)
        
        # 设置坐标轴刻度线在内侧
        ax.tick_params(axis='both', which='major', direction='in', length=6, width=1)
        
        # 设置坐标轴范围
        ax.set_xlim(0, len(predicted_values) - 1)
        
        # 设置网格
        ax.grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
        
        # 使用修复后的字体设置
        ax.set_xlabel("时间步", fontsize=10, fontweight='bold', fontproperties=chinese_font)
        ax.set_ylabel("客流量", fontsize=10, fontweight='bold', fontproperties=chinese_font)
        
        # 设置图例
        legend = ax.legend(loc='upper right', frameon=True, fancybox=True, 
                          shadow=True, framealpha=0.9, fontsize=9, 
                          prop=chinese_font)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_edgecolor('gray')
        legend.get_frame().set_linewidth(0.5)
        
        plt.tight_layout()
        
        # 保存测试图表
        import os
        if not os.path.exists('test_results'):
            os.makedirs('test_results')
        
        plt.savefig('test_results/font_fix_test.png', dpi=300, 
                    bbox_inches='tight', facecolor='white', edgecolor='none')
        plt.close()
        
        print("✓ 基本绘图测试成功")
        
    except Exception as e:
        print(f"✗ 基本绘图测试失败: {e}")
        return False
    
    # 测试改进的绘图函数
    try:
        create_improved_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            station_id=4,
            title=None,  # 隐藏标题
            save_path='test_results/improved_plot_fix_test.png',
            show_plot=False
        )
        print("✓ 改进绘图函数测试成功")
        
    except Exception as e:
        print(f"✗ 改进绘图函数测试失败: {e}")
        return False
    
    return True

def test_peak_analysis_fix():
    """测试峰值分析绘图修复"""
    print("\n测试峰值分析绘图修复...")
    
    try:
        from utils.plot_utils import create_peak_analysis_plot
        
        # 创建测试数据
        np.random.seed(123)
        time_steps = 80
        predicted_values = 60 + 25 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
        actual_values = 60 + 25 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
        
        # 确保值为正数
        predicted_values = np.maximum(predicted_values, 0)
        actual_values = np.maximum(actual_values, 0)
        
        # 生成峰值和低谷数据
        peaks_indices = [20, 45, 70]
        valleys_indices = [10, 35, 60]
        
        peaks_pred_time = peaks_indices
        peaks_pred_val = [predicted_values[i] for i in peaks_indices]
        peaks_true_time = peaks_indices
        peaks_true_val = [actual_values[i] for i in peaks_indices]
        
        valleys_pred_time = valleys_indices
        valleys_pred_val = [predicted_values[i] for i in valleys_indices]
        valleys_true_time = valleys_indices
        valleys_true_val = [actual_values[i] for i in valleys_indices]
        
        # 测试峰值分析绘图
        create_peak_analysis_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            peaks_pred_time=peaks_pred_time,
            peaks_pred_val=peaks_pred_val,
            peaks_true_time=peaks_true_time,
            peaks_true_val=peaks_true_val,
            valleys_pred_time=valleys_pred_time,
            valleys_pred_val=valleys_pred_val,
            valleys_true_time=valleys_true_time,
            valleys_true_val=valleys_true_val,
            station_id=4,
            title=None,  # 隐藏标题
            save_path='test_results/peak_analysis_fix_test.png',
            show_plot=False
        )
        
        print("✓ 峰值分析绘图测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 峰值分析绘图测试失败: {e}")
        return False

if __name__ == "__main__":
    import os
    
    # 创建测试结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    print("=" * 60)
    print("字体修复测试")
    print("=" * 60)
    
    # 运行测试
    basic_test_result = test_font_fix()
    peak_test_result = test_peak_analysis_fix()
    
    print("\n" + "=" * 60)
    if basic_test_result and peak_test_result:
        print("✓ 所有测试通过！字体修复成功")
        print("现在可以正常运行 extract_station_peaks.py")
    else:
        print("✗ 部分测试失败，需要进一步检查")
    print("=" * 60)
    
    print("\n修复说明:")
    print("1. 使用 FontProperties 对象代替字符串设置字体")
    print("2. 避免了 matplotlib 解析带连字符字体名称的问题")
    print("3. 添加了字体设置的异常处理")
    print("4. 保持了方正书宋字体的优先级")
