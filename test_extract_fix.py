#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试extract_station_peaks修复
"""

import numpy as np
import os
from utils.plot_utils import create_peak_analysis_plot

def test_extract_fix():
    """测试峰值分析修复"""
    print("测试峰值分析修复...")
    
    # 创建模拟数据
    np.random.seed(42)
    time_steps = 100
    
    # 生成模拟的客流量数据
    base_flow = 50 + 30 * np.sin(np.linspace(0, 4*np.pi, time_steps))
    noise_pred = np.random.normal(0, 5, time_steps)
    noise_true = np.random.normal(0, 3, time_steps)
    
    predicted_values = base_flow + noise_pred
    actual_values = base_flow + noise_true
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 生成峰值和低谷数据
    peaks_indices = [20, 45, 70, 95]
    valleys_indices = [10, 35, 60, 85]
    
    peaks_pred_time = peaks_indices
    peaks_pred_val = [predicted_values[i] for i in peaks_indices]
    peaks_true_time = peaks_indices
    peaks_true_val = [actual_values[i] for i in peaks_indices]
    
    valleys_pred_time = valleys_indices
    valleys_pred_val = [predicted_values[i] for i in valleys_indices]
    valleys_true_time = valleys_indices
    valleys_true_val = [actual_values[i] for i in valleys_indices]
    
    try:
        # 测试峰值分析绘图
        create_peak_analysis_plot(
            predicted_values=predicted_values,
            actual_values=actual_values,
            peaks_pred_time=peaks_pred_time,
            peaks_pred_val=peaks_pred_val,
            peaks_true_time=peaks_true_time,
            peaks_true_val=peaks_true_val,
            valleys_pred_time=valleys_pred_time,
            valleys_pred_val=valleys_pred_val,
            valleys_true_time=valleys_true_time,
            valleys_true_val=valleys_true_val,
            station_id=4,
            title=None,  # 隐藏标题
            save_path='test_extract_fix.png',
            show_plot=False
        )
        
        print("✓ 峰值分析绘图测试成功")
        print("✓ 字体问题已修复")
        return True
        
    except Exception as e:
        print(f"✗ 峰值分析绘图测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("测试extract_station_peaks修复")
    print("=" * 50)
    
    success = test_extract_fix()
    
    if success:
        print("\n✓ 修复成功！现在可以运行extract_station_peaks.py")
    else:
        print("\n✗ 修复失败，需要进一步检查")
    
    print("=" * 50)
