#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试方正书宋字体设置
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from utils.plot_utils import configure_chinese_font, create_improved_plot

def test_font_availability():
    """测试字体可用性"""
    print("检查系统中可用的中文字体...")
    
    # 获取所有可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 检查常见的中文字体
    chinese_fonts = ['FZShuSong-Z01S', 'SimSun', 'SimHei', 'Microsoft YaHei', 'KaiTi']
    
    print("\n中文字体可用性检查:")
    for font in chinese_fonts:
        if font in available_fonts:
            print(f"✓ {font} - 可用")
        else:
            print(f"✗ {font} - 不可用")
    
    # 查找包含"方正"的字体
    fangzheng_fonts = [f for f in available_fonts if '方正' in f or 'FZ' in f]
    if fangzheng_fonts:
        print(f"\n找到的方正字体: {fangzheng_fonts}")
    else:
        print("\n未找到方正字体")

def test_font_rendering():
    """测试字体渲染效果"""
    print("\n测试字体渲染效果...")
    
    # 配置字体
    configure_chinese_font()
    
    # 创建测试数据
    np.random.seed(42)
    time_steps = 50
    predicted_values = 50 + 20 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    actual_values = 50 + 20 * np.sin(np.linspace(0, 2*np.pi, time_steps)) + np.random.normal(0, 2, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 创建测试图表
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 绘制数据
    time_axis = np.arange(len(predicted_values))
    ax.plot(time_axis, predicted_values, color="red", linewidth=2, label="预测值", alpha=0.8)
    ax.plot(time_axis, actual_values, color="blue", linewidth=2, label="实际值", alpha=0.8)
    
    # 设置坐标轴刻度线在内侧
    ax.tick_params(axis='both', which='major', direction='in', length=6, width=1)
    ax.tick_params(axis='both', which='minor', direction='in', length=3, width=0.5)
    
    # 设置坐标轴范围
    ax.set_xlim(0, len(predicted_values) - 1)
    
    # 设置网格
    ax.grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
    
    # 获取中文字体属性
    try:
        chinese_font = fm.FontProperties(family=['FZShuSong-Z01S', 'SimSun', 'SimHei'])
    except:
        chinese_font = fm.FontProperties(family=['SimSun', 'SimHei'])

    # 设置标签 - 使用方正书宋字体，隐藏标题
    ax.set_xlabel("时间步", fontsize=10, fontweight='bold', fontproperties=chinese_font)
    ax.set_ylabel("客流量", fontsize=10, fontweight='bold', fontproperties=chinese_font)
    # ax.set_title("方正书宋字体测试 - 客流量预测结果", fontsize=12, fontweight='bold',
    #              pad=20, fontproperties=chinese_font)  # 隐藏标题

    # 设置图例
    legend = ax.legend(loc='upper right', frameon=True, fancybox=True,
                      shadow=True, framealpha=0.9, fontsize=9,
                      prop=chinese_font)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_edgecolor('gray')
    legend.get_frame().set_linewidth(0.5)
    
    plt.tight_layout()
    
    # 保存测试图表
    import os
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    plt.savefig('test_results/font_test_fangzheng.png', dpi=300, 
                bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()
    
    print("✓ 字体渲染测试完成，结果保存为: test_results/font_test_fangzheng.png")

def test_improved_plot_with_font():
    """测试改进的绘图函数与字体设置"""
    print("\n测试改进的绘图函数...")
    
    # 创建测试数据
    np.random.seed(123)
    time_steps = 80
    predicted_values = 60 + 25 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 4, time_steps)
    actual_values = 60 + 25 * np.sin(np.linspace(0, 3*np.pi, time_steps)) + np.random.normal(0, 3, time_steps)
    
    # 确保值为正数
    predicted_values = np.maximum(predicted_values, 0)
    actual_values = np.maximum(actual_values, 0)
    
    # 使用改进的绘图函数（无标题）
    create_improved_plot(
        predicted_values=predicted_values,
        actual_values=actual_values,
        station_id=4,
        title=None,  # 隐藏标题
        save_path='test_results/improved_plot_fangzheng.png',
        show_plot=False
    )
    
    print("✓ 改进绘图函数测试完成，结果保存为: test_results/improved_plot_fangzheng.png")

def create_font_comparison():
    """创建字体对比图"""
    print("\n创建字体对比图...")
    
    # 测试数据
    np.random.seed(456)
    time_steps = 30
    data = 40 + 15 * np.sin(np.linspace(0, 2*np.pi, time_steps))
    
    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('中文字体对比测试', fontsize=16, fontweight='bold')
    
    fonts = [
        ('FZShuSong-Z01S', '方正书宋'),
        ('SimSun', '宋体'),
        ('SimHei', '黑体'),
        ('Microsoft YaHei', '微软雅黑')
    ]
    
    for i, (font_name, font_display) in enumerate(fonts):
        ax = axes[i//2, i%2]
        
        # 绘制数据
        ax.plot(data, color='blue', linewidth=2, label='测试数据')
        
        # 设置刻度线在内侧
        ax.tick_params(axis='both', which='major', direction='in', length=6, width=1)
        
        # 设置标签和标题
        try:
            font_prop = fm.FontProperties(family=[font_name])
            ax.set_xlabel("时间步", fontproperties=font_prop, fontsize=10)
            ax.set_ylabel("数值", fontproperties=font_prop, fontsize=10)
            ax.set_title(f"{font_display} ({font_name})", fontproperties=font_prop, fontsize=12, fontweight='bold')
            ax.legend(prop=font_prop)
        except:
            ax.set_xlabel("时间步", fontsize=10)
            ax.set_ylabel("数值", fontsize=10)
            ax.set_title(f"{font_display} (不可用)", fontsize=12, fontweight='bold')
            ax.legend(fontsize=9)
        
        ax.grid(True, linestyle='--', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_results/font_comparison.png', dpi=300, 
                bbox_inches='tight', facecolor='white', edgecolor='none')
    plt.close()
    
    print("✓ 字体对比图创建完成，结果保存为: test_results/font_comparison.png")

if __name__ == "__main__":
    import os
    
    # 创建测试结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    print("=" * 60)
    print("方正书宋字体设置测试")
    print("=" * 60)
    
    # 运行测试
    test_font_availability()
    test_font_rendering()
    test_improved_plot_with_font()
    create_font_comparison()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("所有测试结果保存在 test_results/ 目录下")
    print("=" * 60)
    
    print("\n字体设置说明:")
    print("1. 主要字体: 方正书宋 (FZShuSong-Z01S)")
    print("2. 备用字体: 宋体 (SimSun), 黑体 (SimHei)")
    print("3. 基础字体大小: 8号")
    print("4. 标签字体大小: 10号")
    print("5. 标题字体大小: 12号")
    print("6. 图例字体大小: 9号")
