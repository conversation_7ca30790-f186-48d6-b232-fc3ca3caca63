import torch
from model.main_model import Model

# 首先安装onnx模块（如果尚未安装）
import subprocess
import sys

try:
    import onnx
    print("ONNX模块已安装")
except ImportError:
    print("正在安装ONNX模块...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "onnx"])
    print("ONNX模块安装完成")

# 创建模型实例
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = Model(time_lag=10, pre_len=5, station_num=276, device=device)

# 创建随机输入 - 注意：将batch_size设为1以避免LSTM警告
batch_size = 1  # 修改为1以避免LSTM批处理大小警告
station_num = 276
time_lag = 10
inflow = torch.randn(batch_size, station_num, time_lag*3)
outflow = torch.randn(batch_size, station_num, time_lag*3)
adj = torch.randn(station_num, station_num)

# 添加模型元数据和文档
model_metadata = {
    "producer_name": "地铁客流预测模型",
    "producer_version": "1.0",
    "domain": "交通流量预测",
    "description": "基于GCN-LSTM混合架构的地铁客流预测模型，集成了注意力机制进行特征融合",
    "author": "付艳欣",
    "license": "MIT"
}

# 添加输入输出的文档说明
input_docs = {
    "inflow": "地铁站点的进站客流量数据，形状为[batch_size, station_num, time_lag*3]，包含周、日、时三个时间尺度的特征",
    "outflow": "地铁站点的出站客流量数据，形状为[batch_size, station_num, time_lag*3]，包含周、日、时三个时间尺度的特征",
    "adj": "站点之间的邻接矩阵，形状为[station_num, station_num]，表示站点间的连接关系"
}

output_docs = {
    "output": "预测的未来客流量，形状为[batch_size, station_num, pre_len]，表示每个站点在预测时间窗口内的客流量"
}

# 导出为ONNX
torch.onnx.export(
    model,
    (inflow, outflow, adj),
    "model.onnx",
    input_names=["inflow", "outflow", "adj"],
    output_names=["output"],
    dynamic_axes={
        "inflow": {0: "batch_size"},
        "outflow": {0: "batch_size"},
        "output": {0: "batch_size"}
    },
    opset_version=11,  # 指定ONNX操作集版本
    do_constant_folding=True,  # 执行常量折叠优化
    verbose=False
)

print("模型已导出为ONNX格式，可以在 https://netron.app 上查看")

# 验证导出的ONNX模型并添加元数据
try:
    import onnx
    # 加载ONNX模型
    onnx_model = onnx.load("model.onnx")
    
    # 添加模型元数据
    for key, value in model_metadata.items():
        meta = onnx_model.metadata_props.add()
        meta.key = key
        meta.value = value
    
    # 添加输入文档
    for i, input_name in enumerate(["inflow", "outflow", "adj"]):
        if i < len(onnx_model.graph.input):
            onnx_model.graph.input[i].doc_string = input_docs.get(input_name, "")
    
    # 添加输出文档
    for i, output_name in enumerate(["output"]):
        if i < len(onnx_model.graph.output):
            onnx_model.graph.output[i].doc_string = output_docs.get(output_name, "")
    
    # 添加节点说明
    for node in onnx_model.graph.node:
        if "GCN" in node.name:
            node.doc_string = "图卷积网络层，用于捕获站点之间的空间依赖关系"
        elif "LSTM" in node.name:
            node.doc_string = "长短期记忆网络层，用于捕获客流量随时间变化的长期依赖关系"
        elif "Attention" in node.name:
            node.doc_string = "注意力机制层，为不同站点分配不同的权重"
        elif "Fusion" in node.name:
            node.doc_string = "特征融合层，综合利用空间和时间两个维度的信息"
        elif "Conv2D" in node.name:
            node.doc_string = "二维卷积层，进一步提取特征"
        elif "Linear" in node.name:
            node.doc_string = "全连接层，用于生成最终预测结果"
    
    # 保存添加了元数据的模型
    onnx.save(onnx_model, "model_with_docs.onnx")
    
    # 检查模型是否格式正确
    onnx.checker.check_model(onnx_model)
    print("ONNX模型验证成功！")
    print("带有中文说明的ONNX模型已保存为 model_with_docs.onnx")
except Exception as e:
    print(f"ONNX模型验证或添加元数据失败: {e}")